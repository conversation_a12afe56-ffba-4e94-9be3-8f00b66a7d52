using FluentValidation;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;

namespace Portal.Gateway.UI.Areas.Appointment.ViewModels.Application
{
    public class AddUpdateApplicationViewModel
    {
        public AddUpdateApplicationViewModel()
        {
            ApplicationFormElements = new List<ApplicationFormElement>();
            VisaHistories = new List<VisaHistory>();
            ExtraFees = new List<ExtraFee>();
            ReleatedInsuranceApplicationDetail = new List<ReleatedInsuranceApplication>();
        }

        public bool IsExistingApplication => !string.IsNullOrEmpty(EncryptedApplicationId);

        public bool PrintButton { get; set; }

        public DateTimeOffset ApplicationTime { get; set; }

        public bool IsFromPreApplication { get; set; }

        public string EncryptedApplicationId { get; set; }

        public int BranchApplicationCountryId { get; set; }

        public int ApplicantId { get; set; }

        public string BranchName { get; set; }

        public int CountryId { get; set; }
        public int BranchCountryId { get; set; }

        public string CountryName { get; set; }

        public string CountryCallingCode { get; set; }

        public string InformationNotes { get; set; }

        public bool IsSapApplicationOrderExists { get; set; }

        public int? RelationalApplicationId { get; set; }

        public bool IsMainApplicant { get; set; }

        public bool IsApplicantTypeSet { get; set; }

        public string Note { get; set; }

        public string EncryptedPreApplicationApplicantId { get; set; }

        public string EncryptedTokenId { get; set; }

        public string EncryptedWhiteListId { get; set; }

        public int? VasTypeId { get; set; }

        public int? NumberOfEntryId { get; set; }

        public bool IsVipAppointment { get; set; }

        public int? CustomerId { get; set; }
        public bool IsCargoIntegrationActive { get; set; }
        public bool ShowCityDropdown { get; set; }
        public bool IsPreApplicationConnectionActive { get; set; }
        public int? CargoProviderId { get; set; }
        public bool IsAgency { get; set; }
        public bool IraqOpsiyonelSmsCheck { get; set; }
        public string ConnectedPreApplicationNumber { get; set; }
        public string ConnectedEncryptedPreApplicationApplicantId { get; set; }
        public bool ShowPaymentMethods { get; set; }
        public string QmsScreenTitle { get; set; }

        #region Description

        public int ApplicantTypeId { get; set; }

        public int? ApplicantCount { get; set; }

        public bool ApplicationCountCompleted { get; set; }

        public int ApplicationTypeId { get; set; }

        public int? RelationShipId { get; set; }

        public string PassportNumber { get; set; }

        public DateTime? PassportExpireDate { get; set; }

        public int ApplicationPassportStatusId { get; set; }

        public int? AgencyId { get; set; }

        public string? Reason { get; set; }

        public bool? IsAllowDeniedPassport { get; set; }

        #endregion

        #region BasicInformation

        public int TitleId { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public DateTime BirthDate { get; set; }

        public int GenderId { get; set; }

        public int? MaritalStatusId { get; set; }
        public int? ForeignCityId { get; set; }

        public int NationalityId { get; set; }

        public string ResidenceNumber { get; set; }

        public string MaidenName { get; set; }

        public string FatherName { get; set; }

        public string MotherName { get; set; }

        public string Email { get; set; }

        public string PhoneNumber1 { get; set; }

        public string PhoneNumber2 { get; set; }

        public string Address { get; set; }

        public bool IsContactInformationVerified { get; set; }
        public bool DisableContactInformationVerify { get; set; }

        //public string City { get; set; }
        public string PostalCode { get; set; }
        public string NameOfSecondContactPerson { get; set; }
        public string AreaId { get; set; }
        public string AreaName { get; set; }
        public string GovernorateId { get; set; }
        public string GovernorateName { get; set; }
        public bool IsCargoIntegrationSelected { get; set; }
        public int FamilyIraqSmsSelected { get; set; }
        public int FamilyIraqOpsiyonelSmsSelected { get; set; }

        #endregion

        #region DocumentInformation

        public int? TotalYearInCountry { get; set; }

        public int? ReimbursementTypeId { get; set; }

        public string ReimbursementSponsorDetail { get; set; }

        public string Job { get; set; }
        public int? OccupationId { get; set; }
        public string CompanyName { get; set; }

        public int? TotalYearInCompany { get; set; }

        public int? MonthlySalary { get; set; }

        public int? MonthlySalaryCurrencyId { get; set; }

        public bool HasBankAccount { get; set; }

        public int? BankBalance { get; set; }

        public int? BankBalanceCurrencyId { get; set; }

        public bool HasDeed { get; set; }

        public int VisaCategoryId { get; set; } = 0;

        public int? AdditionalServiceTypeId { get; set; }

        public bool HasEntryBan { get; set; }

        public DateTime EntryDate { get; set; }

        public DateTime ExitDate { get; set; }

        public string CityName { get; set; }
        public string AccomodationDetail { get; set; }

        public bool HasRelativeAbroad { get; set; }

        public string RelativeLocation { get; set; }

        public string PersonTravelWith { get; set; }

        public bool PersonTravelWithHasVisa { get; set; }
        public int? ApplicationTogetherId { get; set; }
        public bool ApplicationTogether
        {
            get { return ApplicationTogetherId switch { (int)YesNoQuestion.Yes => true, (int)YesNoQuestion.No => false, _ => false }; }
        }
        public int? ApplicationTogetherFiftyYearCount { get; set; }
        public int? ApplicationTogetherFifteenYearCount { get; set; }
        public bool IsRequiredApplicationTogether { get; set; } = false;

        public int ResidenceApplication { get; set; }

        public bool? ResidenceApplicationToBeMade
        {
            get { return ResidenceApplication switch { (int)YesNoQuestion.Yes => true, (int)YesNoQuestion.No => false, (int)YesNoQuestion.Unspecified => null, _ => null }; }
        }

        public bool? HasPersonVisitedTurkeyBefore { get; set; }

        public int? VehicleTypeId { get; set; }
        public string VehicleType => EnumHelper.GetEnumDescription(typeof(VehicleType), VehicleTypeId.ToString());
        public int? BrandModelId { get; set; }
        public string PlateNo { get; set; }

        public int? ModelYear { get; set; }

        public string ChassisNumber { get; set; }
        public string BrandText { get; set; }

        public string ModelText { get; set; }

        #endregion

        #region ApplicationFormElement

        public IList<ApplicationFormElement> ApplicationFormElements { get; set; }

        public class ApplicationFormElement
        {
            public int FormElementId { get; set; }

            public bool IsRequired { get; set; }
        }

        #endregion

        #region VisaHistory

        public IList<VisaHistory> VisaHistories { get; set; }

        public class VisaHistory
        {
            public int? ApplicationVisaHistoryId { get; set; }

            public int? CountryId { get; set; }

            public DateTime? FromDate { get; set; }

            public DateTime? UntilDate { get; set; }

            public bool? IsUsed { get; set; }
            public int? NumberOfEntryId { get; set; }
            public int? VisaIsUsedYear { get; set; }
            public int? OldVisaDecisionId { get; set; }
        }

        #endregion

        #region ExtraFee

        public IList<ExtraFee> ExtraFees { get; set; }

        public class ExtraFee
        {
            public int? ApplicationExtraFeeId { get; set; }

            public int ExtraFeeId { get; set; }

            public int TypeId { get; set; }

            public int? MinimumItem { get; set; }

            public int CategoryTypeId { get; set; }

            public string ExtraFeeName { get; set; }

            public bool IsChecked { get; set; }

            public bool IsPreChecked { get; set; }

            public int IsPreQuantity { get; set; }

            public bool IsForRejectedApplications { get; set; }
            public int? RejectionFeePolicyType { get; set; }

            public decimal Price { get; set; }

            public int CurrencyId { get; set; }

            public int Quantity { get; set; }

            public byte? AgeRange { get; set; }

            public bool Cash { get; set; } = false;

            public bool Pos { get; set; } = false;

            public bool Online { get; set; } = false;
            public bool IsMainFee { get; set; }
            public bool IsSubFee { get; set; }
            public int? MainFeeId { get; set; }
            public int? RelatedMainFeeId { get; set; }
            public bool IsTakenFromB2C { get; set; }

        }

        #endregion

        #region RelationalApplication

        public RelationalApplication RelationalApplicationDetail { get; set; }

        public class RelationalApplication
        {
            public int RelationalApplicationId { get; set; }

            public int ApplicantTypeId { get; set; }

            public string Email { get; set; }

            public string PhoneNumber1 { get; set; }

            public string PhoneNumber2 { get; set; }

            public string Address { get; set; }
            public int? ForeignCityId { get; set; }

            //public string City { get; set; }
            public string PostalCode { get; set; }
            public string NameOfSecondContactPerson { get; set; }
            public string AreaId { get; set; }
            public string AreaName { get; set; }
            public string GovernorateId { get; set; }
            public string GovernorateName { get; set; }
            public bool? IsCargoIntegrationSelected { get; set; }


            public RelationalApplicationDocument RelationalApplicationDocumentDetail { get; set; }

            public class RelationalApplicationDocument
            {
                public bool HasBankAccount { get; set; }

                public int? BankBalance { get; set; }

                public int? BankBalanceCurrencyId { get; set; }

                public bool HasDeed { get; set; }

                public int VisaCategoryId { get; set; }

                public int? AdditionalServiceTypeId { get; set; }

                public DateTime EntryDate { get; set; }

                public DateTime ExitDate { get; set; }
                public int? MonthlySalary { get; set; }
                public int? MonthlySalaryCurrencyId { get; set; }
                public string Job { get; set; }
                public int? OccupationId { get; set; }
                public string CompanyName { get; set; }
                public int? TotalYearInCompany { get; set; }
                public string ReimbursementSponsorDetail { get; set; }
                public bool? HasPersonVisitedTurkeyBefore { get; set; }
            }
        }

        #endregion

        public bool? ShowAllowDeniedPassportControl { get; set; }
        public bool IsPrinterIntegrationActive { get; set; }
        public bool IsPrinterMultipleApplicationPage { get; set; }
        public bool IsPhotoBoothIntegrationActive { get; set; }
        public bool IsApplicationUpdateStatusCheckActive { get; set; }
        public bool IsRelatedInsuranceForExemptActive { get; set; }
        public bool? ShowReleatedInvidual { get; set; }
        //public bool IsPhoneNumberRejectionCheckActive => CountryCallingCode == "964";

        public bool IsPhoneNumberRejectionCheckActive { get; set; }

        public bool IsIraqBranch { get; set; }

        public bool IsPhoneNumber2Mandatory => IsIraqBranch;

        #region ReleatedInsurance
        public int? ProvidedWithHasRelatedInsuranceId { get; set; }
        public bool ProvidedWithHasRelatedInsurance
        {
            get { return ProvidedWithHasRelatedInsuranceId switch { (int)YesNoQuestion.Yes => true, (int)YesNoQuestion.No => false, _ => false }; }
        }

        public string? AdditionalParam { get; set; }
        public bool HasNoEmail { get; set; }

        public IList<ReleatedInsuranceApplication> ReleatedInsuranceApplicationDetail { get; set; }
        public class ReleatedInsuranceApplication
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public string Surname { get; set; }
            public DateTime? BirthDate { get; set; }

            [UIHint("~/Areas/Appointment/Views/Application/_Nationality.cshtml")]
            public NationalityViewModel ReleatedInsuranceNationality { get; set; } = new();
            public int ReleatedInsuranceNationalityId { get; set; } = new();
            public string PassportNumber { get; set; }
            public string Email { get; set; }
            public string PhoneNumber1 { get; set; }

        }

        public class NationalityViewModel
        {
            public int Id { get; set; }

            public string Name { get; set; }
        }
        #endregion
    }

    public class AddUpdateApplicationViewModelValidator : AbstractValidator<AddUpdateApplicationViewModel>
    {
        public AddUpdateApplicationViewModelValidator()
        {
            #region Description

            RuleFor(x => x.ApplicantTypeId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.PassportNumber)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.ApplicationTypeId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.ApplicationPassportStatusId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            #endregion

            #region BasicInformation

            RuleFor(x => x.TitleId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.Surname)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.BirthDate)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.GenderId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.NationalityId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.FatherName)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.MotherName)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.Email)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .EmailAddress().WithMessage(SiteResources.InvalidFormat);//// doğrulama validasyonu yapılacak

            RuleFor(x => x.PhoneNumber1)
                .NotEmpty().WithMessage(SiteResources.RequiredField);//// doğrulama validasyonu yapılacak

            RuleFor(x => x.PhoneNumber2)
                .NotEmpty()
                .When(x => x.IsPhoneNumberRejectionCheckActive)
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.Address)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.ForeignCityId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField).When(c => c.ShowCityDropdown);

            RuleFor(x => x.IsContactInformationVerified)
                .NotNull().WithMessage(SiteResources.RequiredField).When(k => !k.DisableContactInformationVerify)
                .Must(x => x == true).WithMessage("Doğrulama gerekli").When(k => !k.DisableContactInformationVerify);

            #endregion

            #region DocumentInformation

            RuleFor(x => x.VisaCategoryId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.EntryDate)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.ExitDate)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            #endregion
        }
    }

    public class ReleatedInsuranceApplicationDetailValidator : AbstractValidator<AddUpdateApplicationViewModel.ReleatedInsuranceApplication>
    {
        public ReleatedInsuranceApplicationDetailValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.Surname)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.BirthDate)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.ReleatedInsuranceNationalityId)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.PassportNumber)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.Email)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.PhoneNumber1)
                .NotEmpty()
                .WithMessage(SiteResources.RequiredField);
        }
    }
}
