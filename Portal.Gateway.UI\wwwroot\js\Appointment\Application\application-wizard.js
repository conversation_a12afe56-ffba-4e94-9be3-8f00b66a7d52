"use strict";
var checkNll = false;

$(function () {
  $("#submitButtonApplication").prop("disabled", true);
  $("#submitButtonApplicationPrinter").prop("disabled", true);

  var isExistingApplication = $("#IsExistingApplication").val();
  var isFromPreApplication = $("#IsFromPreApplication").val();
  if (
    (isExistingApplication === "false" ||
      isExistingApplication === "False" ||
      isExistingApplication === false) &&
    (isFromPreApplication === "false" ||
      isFromPreApplication === "False" ||
      isFromPreApplication === false)
  ) {
    $("#PassportExpireDate").val("");
    $("#BirthDate").val("");
  }

  ApplicationWizard.init();
  checkExtraPackageUpdateAllowed();
  checkContactInformationUpdateAllowed();
});

var isFormValid;
var isVisaExemptOkButtonClicked = false;
var phoneRejectionSkip = false;

var ApplicationWizard = (function () {
  var _wizardEl;
  var _wizardObj;
  var _validations = [];
  var _tabTitles = [];

  var _initWizard = function () {
    _wizardObj = new KTWizard(_wizardEl, {
      startStep: 1,
      clickableSteps: true,
    });

    _wizardObj.on("change", function (wizard) {
      if (wizard.getStep() > wizard.getNewStep()) {
        return;
      }

      isFormValid = true;

      var isApplicationDocumentIgnored = $(
        "#IsApplicationDocumentIgnored"
      ).val();

      for (var i = 0; i < wizard.getNewStep() - 1; i++) {
        var validator = _validations[i];

        if (i === 2 && isApplicationDocumentIgnored === "true") {
          //
        } else {
          if (validator) {
            if (validator.validate()) {
              //
            } else {
              isFormValid &= false;
              bootbox.dialog({
                message:
                  '<p style="color:#FFFFFF;"><b>' +
                  jsResources.FillRequiredFields +
                  " (" +
                  _tabTitles[i] +
                  ")" +
                  "</b></p>",
                size: "extra-large",
                onEscape: true,
                backdrop: true,
              });
              $(".modal-content").css("background-color", "#FFA800");
              KTUtil.scrollTop();
            }
          }
        }
      }
      if (wizard.getStep() === 1) {
        checkExtraFees();
        checkNll = false;

        var isAllowDeniedPassport = $("#IsAllowDeniedPassport").val();
        var relationShipId = $("#RelationShipId").val();
        var relationalApplicationId = $("#RelationalApplicationId").val();
        var passportNumber = $("#PassportNumber").val();

        if (!checkApplicationTypeForNonApplicationRelatedInsurance()) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.NonApplicationRelatedInsuranceNotAllowed +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
          return false;
        }

        if (checkPassportValidityPeriod() && isAllowDeniedPassport == "true") {
          IsAllowedDeniedPassportNumber(isAllowDeniedPassport).then(
            function () {
              checkPreviousNote().then(function () {
                if (relationShipId === "1") {
                  checkWifeOrHusbandSelection(
                    relationShipId,
                    relationalApplicationId,
                    passportNumber
                  ).then(function () {
                    goToNewStep(wizard);
                  });
                } else if (relationShipId !== "1") {
                  goToNewStep(wizard);
                } else {
                  isFormValid &= false;
                }
              });
            }
          );
        } else if (checkPassportValidityPeriod()) {
          checkApplicationsByPassportNumber().then(function () {
            checkPreviousNote().then(function () {
              if (relationShipId === "1") {
                checkWifeOrHusbandSelection(
                  relationShipId,
                  relationalApplicationId,
                  passportNumber
                ).then(function () {
                  goToNewStep(wizard);
                });
              } else if (relationShipId !== "1") {
                goToNewStep(wizard);
              } else {
                isFormValid &= false;
              }
            });
          });

          if (
            $("#EncryptedApplicationId").val() != "" &&
            ($("#ApplicantTypeId").val() == "2" ||
              $("#ApplicantTypeId").val() == "3") &&
            !checkApplicantCount()
          ) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                jsResources.CanNotReduceApplicantCount +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          }
        } else {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.PassportValidityPeriodLessThan180Days +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }
      } else if (wizard.getStep() === 2) {
        if (checkBirthDate() && emailFormatCheck()) {
          checkApplicationsByPersonalInformation().then(function () {
            function proceedAfterPhoneCheck() {
              if (isFormValid && !isVisaExemptOkButtonClicked) {
                checkVisaExemption(wizard);
              } else {
                goToNewStep(wizard);
              }
            }

            const isPhoneCheck =
              $("#IsPhoneRejectionCheck").val() === "true" ||
              $("#IsPhoneRejectionCheck").val() === "True";
            const hasPhones =
              $("#PhoneNumber1").val() || $("#PhoneNumber2").val();

            if (
              isFormValid &&
              isPhoneCheck &&
              hasPhones &&
              !phoneRejectionSkip
            ) {
              checkRejectedApplicationsByPhoneNumbers().then(function () {
                if (!isFormValid) {
                  // User must click next again to skip; set phoneRejectionSkip=true on next click
                  phoneRejectionSkip = true;
                  return; // stop to prevent goToNewStep
                }
                proceedAfterPhoneCheck();
              });
            } else {
              proceedAfterPhoneCheck();
            }
          });
        } else if (!checkBirthDate()) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.BirthDateValidation +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        } else {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.Exeption_EmailFormat +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }
      } else if (wizard.getStep() === 3) {
        checkAdditionalServiceTypeExtraFee();
        checkVehicleTypeExtraFee();
        if (checkEntryDateIsAvailable()) {
          checkPreviousJob().then(function () {
            //  passport expire date validity check for visa type in TM
            if (checkPassportValidityPeriodForVisaType()) {
              goToNewStep(wizard);
            } else {
              bootbox.dialog({
                message:
                  '<p style="color:#FFFFFF;"><b>' +
                  jsResources.PassportValidityPeriodForVisaTypeWarning +
                  "</b></p>",
                size: "extra-large",
                onEscape: true,
                backdrop: true,
              });
              $(".modal-content").css("background-color", "#FFA800");
              isFormValid &= false;
            }
          });
        } else {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.EntryDateCannotBeLessThanTheApplicationTime +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }
        if ($("#IsExistingApplication").val() === "False") getVasType();
        if (!checkEntryExitDate()) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.EnryExitDateWarning +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }
        if (!checkExitDate()) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.ExitDateWarning +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
        }

        if (
          $("#IsRequiredApplicationTogether").val() == "true" &&
          $("#ApplicationTogetherId").val() == "1" &&
          ($("#ApplicationTogetherFiftyYearCount").val() == "" ||
            $("#ApplicationTogetherFiftyYearCount").val() == "0") &&
          ($("#ApplicationTogetherFifteenYearCount").val() == "" ||
            $("#ApplicationTogetherFifteenYearCount").val() == "0")
        ) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.ApplicationTogetherRequirementWarning +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }
        var grid1 = $("#gridReleatedInsuranceApplication").data("kendoGrid");
        var data1 = grid1.dataSource.view();
        var requiredCheck = false;
        if (
          $("#ProvidedWithHasRelatedInsuranceId").val() == "1" &&
          data1.length == 0
        ) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.HasRelatedInsuranceIsRequired +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }

        if (
          $("#ProvidedWithHasRelatedInsuranceId").val() == "1" &&
          data1.length > 0
        ) {
          for (let item of data1) {
            if (!item.PhoneNumber1 || item.PhoneNumber1.trim() === "") {
              bootbox.dialog({
                message:
                  '<p style="color:#FFFFFF;"><b>' +
                  jsResources.FillRequiredFields +
                  ":PhoneNumber1" +
                  "</b></p>",
                size: "extra-large",
                onEscape: true,
                backdrop: true,
              });
              $(".modal-content").css("background-color", "#ff0000");
              requiredCheck = true;
              isFormValid &= false;
            }
            if (!item.Email || item.Email.trim() === "") {
              bootbox.dialog({
                message:
                  '<p style="color:#FFFFFF;"><b>' +
                  jsResources.FillRequiredFields +
                  ":Email" +
                  "</b></p>",
                size: "extra-large",
                onEscape: true,
                backdrop: true,
              });
              $(".modal-content").css("background-color", "#ff0000");
              requiredCheck = true;
              isFormValid &= false;
            }
          }

          if (requiredCheck == false) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                jsResources.HasRelatedInsuranceWarning +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
          }
        }

        if ($("#PlateNo").val()) {
          onCheckPlateNo();
        }

        if ($("#ChassisNumber").val()) {
          onCheckChassisNumber();
        }
      } else if (wizard.getStep() === 4) {
        var grid1 = $("#gridReleatedInsuranceApplication").data("kendoGrid");
        var data1 = grid1.dataSource.view();

        if (
          $("#ProvidedWithHasRelatedInsuranceId").val() == "1" &&
          data1.length == 0
        ) {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.HasRelatedInsuranceIsRequired +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          isFormValid &= false;
        }

        if (
          $("#ProvidedWithHasRelatedInsuranceId").val() == "1" &&
          data1.length > 0
        ) {
          var quantity = checkReleatedInsuranceExtraFeeCount();
          if (quantity != data1.length) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                jsResources.HasRelatedInsuranceWarningCount +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          }
        }

        if ($("#ShowPaymentMethods").val() == "True") {
          if (!checkPaymentMethods()) {
            isFormValid &= false;
          } else {
            goToNewStep(wizard);
          }
        } else {
          goToNewStep(wizard);
        }
      } else {
        goToNewStep(wizard);
      }

      return false;
    });

    _wizardObj.on("changed", function (wizard) {
      KTUtil.scrollTop();

      if (wizard.getStep() === 3) {
        checkVisaRejectionRefundForNewApplication();
      }

      if (wizard.getStep() === 4) {
        checkExtraFeesForVisaCategory();
        checkPolicyPeriod();
        checkAdditionalServiceTypeExtraFee();
        checkVehicleTypeExtraFee();
      }

      if (wizard.getStep() === wizard.totalSteps) {
        $("#submitButtonApplication").prop("disabled", false);
        $("#submitButtonApplicationPrinter").prop("disabled", false);
      } else {
        $("#submitButtonApplication").prop("disabled", true);
        $("#submitButtonApplicationPrinter").prop("disabled", true);
      }
    });

    _wizardObj.on("submit", function (wizard) {
      var validator = _validations[wizard.getStep() - 1];

      if (validator) {
        if (validator.validate()) {
          var isExistingApplication = $("#IsExistingApplication").val();

          if (
            isExistingApplication === "true" ||
            isExistingApplication === "True" ||
            isExistingApplication === true
          ) {
            if (
              $("#IsSapApplicationOrderExists").val() === "false" ||
              $("#IsSapApplicationOrderExists").val() === "False"
            ) {
              bootbox.confirm(
                jsResources.ConfirmUpdateApplicationNotToSendToSap,
                function (result) {
                  if (result) {
                    updateApplication();
                  }
                }
              );
            } else {
              updateApplication();
            }
          } else {
            var PrinterApplicationId = 0;
            var PrinterRelationalApplicationId = 0;
            var ApplicantTypeId = 0;
            var encryptedApplicationId;
            var encryptedRelationalApplicationId;
            var printButton = $("#PrintButton").val();
            var visaCategoryId = $("#VisaCategoryId").val();
            var isPrinterMultipleApplicationPage = $(
              "#IsPrinterMultipleApplicationPage"
            ).val();
            $("#submitButtonApplication").prop("disabled", true);

            var grid = $("#gridReleatedInsuranceApplication").data("kendoGrid");
            var data = grid.dataSource.view();
            var jsonData = JSON.stringify(data); // JSON formatına çevir

            $.ajax({
              type: "POST",
              url: "/Appointment/Application/AddApplication",
              async: false,
              data:
                $("#formAddUpdateApplication").serialize() +
                `&AdditionalParam=${encodeURIComponent(jsonData)}`,
              success: function (data) {
                $("#submitButtonApplication").prop("disabled", false);
                showNotification(data.Type, data.Message);
                if (data.Type === "success") {
                  $("#submitButtonApplication").prop("disabled", true);
                  ApplicantTypeId = $("#ApplicantTypeId").val();
                  PrinterApplicationId = data.Data.Id;
                  PrinterRelationalApplicationId = $(
                    "#RelationalApplicationId"
                  ).val();

                  encryptedApplicationId = getItemTextValue(
                    data.Data.Id,
                    "ToEncrypt"
                  );
                  encryptedRelationalApplicationId = getItemTextValue(
                    PrinterRelationalApplicationId,
                    "ToEncrypt"
                  );

                  if (printButton == "True" || printButton == "true") {
                    if (visaCategoryId != 7) {
                      createInsuranceWithPrint(encryptedApplicationId);
                    }

                    if (
                      (ApplicantTypeId == "2" || ApplicantTypeId == "3") &&
                      PrinterApplicationId != ""
                    ) {
                      $.ajax({
                        type: "GET",
                        url:
                          "/Appointment/Application/IsLastApplication?ApplicationId=" +
                          PrinterApplicationId,
                        async: false,
                        success: function (data) {
                          if (data.Type === "success") {
                            if (data.Data.Result === true) {
                              SapCreateOrder(encryptedRelationalApplicationId);
                              print(
                                PrinterApplicationId,
                                PrinterRelationalApplicationId,
                                isPrinterMultipleApplicationPage
                              );
                            }
                          }
                        },
                        error: function () {
                          showNotification("danger", jsResources.ErrorOccurred);
                        },
                      });
                    } else if (PrinterApplicationId != "") {
                      SapCreateOrder(encryptedApplicationId);
                      print(
                        PrinterApplicationId,
                        PrinterRelationalApplicationId,
                        false
                      );
                    }
                  }

                  let encryptedBranchApplicationCountryId = getItemTextValue(
                    $("#BranchApplicationCountryId").val(),
                    "ToEncrypt"
                  );
                  let encryptedApplicantTypeId = getItemTextValue(
                    $("#ApplicantTypeId").val(),
                    "ToEncrypt"
                  );
                  let encryptedApplicantId = getItemTextValue(
                    $("#ApplicantId").val(),
                    "ToEncrypt"
                  );
                  let isInsuranceExist =
                    IsNotCreatedInsuranceExist(PrinterApplicationId);
                  let url = "/Appointment/Application/Completed?";
                  url +=
                    "encryptedBranchApplicationCountryId=" +
                    encryptedBranchApplicationCountryId;
                  url += "&encryptedApplicationId=" + encryptedApplicationId;
                  url +=
                    "&encryptedRelationalApplicationId=" +
                    encryptedRelationalApplicationId;
                  url +=
                    "&encryptedApplicantTypeId=" + encryptedApplicantTypeId;
                  url += "&encryptedApplicantId=" + encryptedApplicantId;
                  url += "&isUpdate=" + "false";
                  url += "&isInsuranceExist=" + isInsuranceExist;
                  url +=
                    "&isPrinterIntegrationActive=" +
                    $("#IsPrinterIntegrationActive").val();
                  url += "&PrintButton=" + printButton;
                  window.location.href = url;
                }
              },
              error: function (data) {
                $("#submitButtonApplication").prop("disabled", false);
                showNotification("danger", jsResources.ErrorOccurred);
              },
            });
          }
        } else {
          bootbox.dialog({
            message:
              '<p style="color:#FFFFFF;"><b>' +
              jsResources.FillRequiredFields +
              "</b></p>",
            size: "extra-large",
            onEscape: true,
            backdrop: true,
          });
          $(".modal-content").css("background-color", "#FFA800");
          KTUtil.scrollTop();
        }
      }
    });
  };

  function checkReleatedInsuranceExtraFeeCount() {
    var result = 0;
    $(extraFees).each(function (i) {
      var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
      var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
      if (
        $("#ExtraFees_" + index + "__IsChecked").is(":checked") &&
        categoryTypeId == 9
      ) {
        result = $("#ExtraFees_" + index + "__Quantity").val();
      }
    });
    return result;
  }

  var _initValidation = function () {
    _validations.push(
      $("#divStep1Description").kendoValidator().data("kendoValidator")
    );
    _validations.push(
      $("#divStep2BasicInformation").kendoValidator().data("kendoValidator")
    );
    _validations.push(
      $("#divStep3DocumentInformation").kendoValidator().data("kendoValidator")
    );
    _validations.push(
      $("#divStep4ExtraPackages").kendoValidator().data("kendoValidator")
    );
    _validations.push(
      $("#divStep5Finalize").kendoValidator().data("kendoValidator")
    );
  };

  var _initTabTitle = function () {
    _tabTitles.push(jsResources.ApplicationStep1Description);
    _tabTitles.push(jsResources.ApplicationStep2BasicInformation);
    _tabTitles.push(jsResources.ApplicationStep3DocumentInformation);
    _tabTitles.push(jsResources.ApplicationStep4ExtraPackages);
    _tabTitles.push(jsResources.ApplicationStep5Finalize);
  };

  return {
    init: function () {
      _wizardEl = KTUtil.getById("wizardAddApplication");

      _initWizard();
      _initValidation();
      _initTabTitle();
    },
  };
})();

function showRejectedApplicationFees(policyType) {
  var showMessage = false;
  const attributeName = "data-fee";

  if (policyType) {
    $(`[${attributeName}]`).each(function () {
      const data = JSON.parse($(this).attr(attributeName));
      if (data.policyType === String(policyType)) {
        $(this).show().find('input[type="number"]').prop("disabled", false);
        $(this)
          .show()
          .find('input[type="checkbox"]')
          .not(".paymentShowHide_" + data.index + ' input[type="checkbox"]')
          .prop("checked", true);

        $(this).removeAttr(attributeName);
        showMessage = true;
      }
    });
  }

  if (showMessage)
    showNotification("success", jsResources.RejectedFeesMadeVisible);
}
function updateApplication() {
  var PrinterApplicationId = 0;
  var PrinterRelationalApplicationId = 0;
  var ApplicantTypeId = 0;
  var printButton = $("#PrintButton").val();
  var visaCategoryId = $("#VisaCategoryId").val();

  var grid = $("#gridReleatedInsuranceApplication").data("kendoGrid");
  var data = grid.dataSource.view();
  var jsonData = JSON.stringify(data); // JSON formatına çevir

  $.ajax({
    type: "PUT",
    url: "/Appointment/Application/UpdateApplication",
    async: false,
    data:
      $("#formAddUpdateApplication").serialize() +
      `&AdditionalParam=${encodeURIComponent(jsonData)}`,
    success: function (data) {
      showNotification(data.Type, data.Message);
      if (data.Type === "success") {
        $("#submitButtonApplication").prop("disabled", true);
        ApplicantTypeId = $("#ApplicantTypeId").val();
        PrinterApplicationId = getItemTextValue(
          $("#EncryptedApplicationId").val(),
          "ToDecryptInt"
        );
        PrinterRelationalApplicationId = $("#RelationalApplicationId").val();

        if (printButton == "True" || printButton == "true") {
          if (visaCategoryId != 7) {
            createInsuranceWithPrint($("#EncryptedApplicationId").val());
          }

          if (
            (ApplicantTypeId == "2" || ApplicantTypeId == "3") &&
            PrinterApplicationId != ""
          ) {
            $.ajax({
              type: "GET",
              url:
                "/Appointment/Application/IsLastApplication?ApplicationId=" +
                PrinterApplicationId,
              async: false,
              success: function (data) {
                if (data.Type === "success") {
                  if (data.Data.Result === true) {
                    print(
                      PrinterApplicationId,
                      PrinterRelationalApplicationId,
                      false
                    );
                  }
                }
              },
              error: function () {
                showNotification("danger", jsResources.ErrorOccurred);
              },
            });
          } else if (PrinterApplicationId != "") {
            print(PrinterApplicationId, PrinterRelationalApplicationId, false);
          }
        }

        let encryptedBranchApplicationCountryId = getItemTextValue(
          $("#BranchApplicationCountryId").val(),
          "ToEncrypt"
        );
        let encryptedApplicationId = $("#EncryptedApplicationId").val();
        let encryptedRelationalApplicationId = getItemTextValue(
          $("#RelationalApplicationId").val(),
          "ToEncrypt"
        );
        let encryptedApplicantTypeId = getItemTextValue(
          $("#ApplicantTypeId").val(),
          "ToEncrypt"
        );
        let isInsuranceExist = IsNotCreatedInsuranceExist(PrinterApplicationId);
        let url = "/Appointment/Application/Completed?";
        url +=
          "encryptedBranchApplicationCountryId=" +
          encryptedBranchApplicationCountryId;
        url += "&encryptedApplicationId=" + encryptedApplicationId;
        url +=
          "&encryptedRelationalApplicationId=" +
          encryptedRelationalApplicationId;
        url += "&encryptedApplicantTypeId=" + encryptedApplicantTypeId;
        url += "&isUpdate=" + "true";
        url += "&isInsuranceExist=" + isInsuranceExist;
        url +=
          "&isPrinterIntegrationActive=" +
          $("#IsPrinterIntegrationActive").val();
        url += "&PrintButton=" + printButton;
        window.location.href = url;
      }
    },
    error: function (data) {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
}

function getVasType() {
  if ($("#BranchApplicationCountryId").val() == "37") {
    var passportNumber = $("#PassportNumber").val();
    var branchApplicationCountryId = $("#BranchApplicationCountryId").val();
    $.ajax({
      type: "GET",
      url:
        "/Appointment/Application/GetApplicationVasType?passportNumber=" +
        passportNumber +
        "&branchApplicationCountryId=" +
        branchApplicationCountryId,
      async: false,
      success: function (data) {
        if (data == 1 || data == 2 || data == 6) {
          $(extraFees).each(function (i) {
            var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
            var categoryTypeId = $(
              "#ExtraFees_" + index + "__CategoryTypeId"
            ).val();
            var extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
            if (categoryTypeId == 1) {
              if (data == 1 && extraFeeId == 8)
                //PrimeTime
                $(extraFees[i]).prop("checked", true);
              else if (data == 2 && extraFeeId == 7)
                //VIP
                $(extraFees[i]).prop("checked", true);
              else if (data == 6 && extraFeeId == 105)
                //FlexibleAppointment
                $(extraFees[i]).prop("checked", true);
            }
          });
        }
      },
      dataType: "json",
    });
  }
  return true;
}

function onCheckPlateNo() {
  var plateNo = $("#PlateNo").val().trim();
  var plateRegex = /^999\s[ABCDEFGHIJKLMNOPRSTUVYZ]{1,3}[0-9]{2,4}$/;

  if (!plateRegex.test(plateNo)) {
    bootbox.dialog({
      message:
        '<p style="color:#FFFFFF;"><b>' +
        jsResources.InvalidPlateFormat +
        "</b></p>",
      size: "extra-large",
      onEscape: true,
      backdrop: true,
    });
    $(".modal-content").css("background-color", "#FFA800");
    isFormValid &= false;
  }
}

function onCheckChassisNumber() {
  var chassisNumber = $("#ChassisNumber").val().trim();
  var chassisRegex = /^[A-HJ-NPR-Z0-9]{17}$/;

  if (!chassisRegex.test(chassisNumber)) {
    bootbox.dialog({
      message:
        '<p style="color:#FFFFFF;"><b>' +
        jsResources.InvalidChassisNumber +
        "</b></p>",
      size: "extra-large",
      onEscape: true,
      backdrop: true,
    });
    $(".modal-content").css("background-color", "#FFA800");
    isFormValid &= false;
  }
}

function checkPassportValidityPeriod() {
  var result = true;
  const FormElements = isRequired();
  if (FormElements[0] !== 0) {
    //if array null returns 0
    const FormElementPassportExpireDateId = new Array();
    for (let i = 0; i < FormElements.length; i++) {
      if (FormElements[i] != null)
        FormElementPassportExpireDateId.push(FormElements[i]);
    }
    var isExist = FormElementPassportExpireDateId.indexOf(16);
    if (isExist != -1) {
      var passportExpireDate = kendo.parseDate(
        $("#PassportExpireDate").val(),
        jsResources.DatePickerFormatJs
      );
      var todayDate = new Date(new Date().toDateString());
      var diff = new Date(passportExpireDate - todayDate);
      var days = diff / 1000 / 60 / 60 / 24;
      if (days < 180) result = false;
    }
  }
  return result;
}

function checkPassportValidityPeriodForVisaType() {
  var countryCallingCode = $("#CountryCallingCode").val();
  var visaTypeId = $("#VisaCategoryId").val();
  var isExistingApplication = $("#IsExistingApplication").val();
  var passportExpireDate = kendo.parseDate(
    $("#PassportExpireDate").val(),
    jsResources.DatePickerFormatJs
  );
  var todayDate = new Date(new Date().toDateString());
  var diff = new Date(passportExpireDate - todayDate);
  var days = diff / 1000 / 60 / 60 / 24;
  var isValid = true;

  if (
    isExistingApplication === "true" ||
    isExistingApplication === "True" ||
    isExistingApplication === true
  ) {
    return isValid;
  }

  if (
    countryCallingCode === "993" &&
    (visaTypeId === "40" || visaTypeId === "41") &&
    days < 730
  ) {
    // 2 years = 730 days and 993 = Turkmenistan
    isValid = false;
  }

  return isValid;
}

function checkApplicationTypeForNonApplicationRelatedInsurance() {
  const isExistingApplication = $("#IsExistingApplication").val();
  const applicationTypeId = $("#ApplicationTypeId").val();

  if (["true", "True", true].includes(isExistingApplication)) {
    return true;
  }

  // "NonApplicationRelatedInsurance" (ID: 14)
  return applicationTypeId != 14;
}

function checkEntryDateIsAvailable() {
  var todayDate = new Date(new Date().toDateString());
  var entryDate = kendo.parseDate(
    $("#EntryDate").val(),
    jsResources.DatePickerFormatJs
  );
  var applicationTime = kendo.parseDate(
    $("#ApplicationTime").val(),
    jsResources.DatePickerFormatJs
  );
  var isExistingApplication = $("#IsExistingApplication").val();

  if (
    (isExistingApplication === "true" ||
      isExistingApplication === "True" ||
      isExistingApplication === true) &&
    entryDate < applicationTime
  ) {
    return false;
  } else if (
    (isExistingApplication === "false" ||
      isExistingApplication === "False" ||
      isExistingApplication === false) &&
    entryDate < todayDate
  ) {
    return false;
  } else return true;
}

function checkEntryExitDate() {
  let exitDate = kendo.parseDate(
    $("#ExitDate").val(),
    jsResources.DatePickerFormatJs
  );
  let entryDate = kendo.parseDate(
    $("#EntryDate").val(),
    jsResources.DatePickerFormatJs
  );
  let diff = new Date(exitDate - entryDate);
  let days = diff / 1000 / 60 / 60 / 24;
  if (days < 0) {
    return false;
  } else return true;
}

function checkExitDate() {
  let exitDate = kendo.parseDate(
    $("#ExitDate").val(),
    jsResources.DatePickerFormatJs
  );
  let today = new Date(new Date().toDateString());
  let diff = new Date(exitDate - today);
  let days = diff / 1000 / 60 / 60 / 24;
  if (days > 1095) {
    //3 years
    return false;
  } else return true;
}

function checkPolicyPeriod() {
  let passportExpireDate = kendo.parseDate(
    $("#PassportExpireDate").val(),
    jsResources.DatePickerFormatJs
  );
  let todayDate = new Date(new Date().toDateString());
  let diff = new Date(passportExpireDate - todayDate);
  let days = diff / 1000 / 60 / 60 / 24;
  let addedToday = todayDate;
  addedToday.setMonth(todayDate.getMonth() + 3);
  addedToday.setFullYear(todayDate.getFullYear() + 1);
  if (days <= 365 || (days >= 365 && passportExpireDate < addedToday)) {
    if ($("#CountryCallingCode").val() == "964") {
      //Iraq
      let extraFee = "";
      $(extraFees).each(function (i) {
        let index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
        let categoryTypeId = $(
          "#ExtraFees_" + index + "__CategoryTypeId"
        ).val();
        if (categoryTypeId == 2) {
          //Insurance
          extraFee = extraFee + "," + extraFeeId;
        }
      });
      if (extraFee != null) {
        $.ajax({
          type: "GET",
          url:
            "/Appointment/Application/IsInsuranceAllowsForMoreThanOneYear?extraFee=" +
            extraFee,
          async: false,
          success: function (data) {
            if (data.Data !== null) {
              for (const element of data) {
                $(extraFees).each(function (i) {
                  let index = extraFees[i].id
                    .split("ExtraFees_")[1]
                    .split("_")[0];
                  let extraFeeId = $(
                    "#ExtraFees_" + index + "__ExtraFeeId"
                  ).val();
                  if (extraFeeId == element) {
                    $(extraFees[i]).prop("checked", false);
                    $(extraFees[i]).prop("disabled", true);
                  }
                });
              }
            }
          },
          dataType: "json",
        });
      }
    }
    if ($("#VisaCategoryId").val() != "7") {
      //Work Permit
      if ($("#CountryCallingCode").val() == "993" && days <= 365) {
        //Turkmenistan
        bootbox.dialog({
          message:
            '<p style="color:#FFFFFF;"><b>' +
            jsResources.PolicyPeriodAccordingToPassportExpireDate +
            '</b></p> <p style="color:#FFFFFF;"><b>' +
            jsResources.MultipleEntryVisaAccordingToPassportExpireDate +
            "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
      } else if (
        ($("#CountryCallingCode").val() != "964" && days <= 365) ||
        $("#CountryCallingCode").val() == "964"
      ) {
        // Iraq and Others
        bootbox.dialog({
          message:
            '<p style="color:#FFFFFF;"><b>' +
            jsResources.PolicyPeriodAccordingToPassportExpireDate +
            "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
      }
      $(".modal-content").css("background-color", "#FFA800");
    }
  }
  return true;
}

function isRequired() {
  const FormElementIds = new Array();
  let branchApplicationCountryId = $("#BranchApplicationCountryId").val();
  $.ajax({
    type: "GET",
    url:
      "/Appointment/Application/IsRequired?branchApplicationCountryId=" +
      branchApplicationCountryId,
    async: false,
    success: function (data) {
      if (data !== null) {
        for (let i = 0; i < data.length; i++) {
          if (data[i] != null) FormElementIds.push(data[i]);
        }
      } else FormElementIds.push(0);
    },
    dataType: "json",
  });
  return FormElementIds;
}

function checkApplicationsByPassportNumber() {
  var passportNumber = $("#PassportNumber").val();
  var countryId = $("#CountryId").val();
  var nationalityId = $("#NationalityId").val();
  var isNewEntryNonApplication = $("#ApplicationTypeId").val();
  var encryptedApplicationId = $("#EncryptedApplicationId").val();

  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/IsApplicationAvailableByPassportNumber",
      data: {
        passportNumber: passportNumber,
        countryId: countryId,
        nationalityId: nationalityId,
        isNewEntryNonApplication: isNewEntryNonApplication,
        encryptedApplicationId: encryptedApplicationId,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            //
          } else if (data.ErrorMessage) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                data.ErrorMessage +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
        isFormValid &= false;
      },
      complete: function () {
        resolve();
      },
    });
  });
}

function IsAllowedDeniedPassportNumber(isAllowDeniedPassport) {
  var passportNumber = $("#PassportNumber").val();
  var countryId = $("#CountryId").val();
  var nationalityId = $("#NationalityId").val();
  var encryptedApplicationId = $("#EncryptedApplicationId").val();
  var isExistingApplication = $("#IsExistingApplication").val();

  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/IsAllowedDeniedPassportNumber",
      data: {
        passportNumber: passportNumber,
        countryId: countryId,
        nationalityId: nationalityId,
        isAllowDeniedPassport: isAllowDeniedPassport,
        encryptedApplicationId: encryptedApplicationId,
        isExistingApplication: isExistingApplication,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            //
          } else if (data.ErrorMessage) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                data.ErrorMessage +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
        isFormValid &= false;
      },
      complete: function () {
        resolve();
      },
    });
  });
}

function checkWifeOrHusbandSelection(
  relationShipId,
  relationalApplicationId,
  passportNumber
) {
  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/RelationShipCheck",
      data: {
        relationShipId: relationShipId,
        relationalApplicationId: relationalApplicationId,
        passportNumber: passportNumber,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            //
          } else if (data.ErrorMessage) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                data.ErrorMessage +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
        isFormValid &= false;
      },
      complete: function () {
        resolve();
      },
    });
  });
}

function checkApplicationsByPersonalInformation() {
  var name = $("#Name").val();
  var surname = $("#Surname").val();
  var birthDate = $("#BirthDate").val();
  var maidenName = $("#MaidenName").val();
  var fatherName = $("#FatherName").val();
  var motherName = $("#MotherName").val();
  var countryId = $("#CountryId").val();
  var nationalityId = $("#NationalityId").val();
  var encryptedApplicationId = $("#EncryptedApplicationId").val();
  var isAllowDeniedPassport = $("#IsAllowDeniedPassport").val();
  var passportNumber = $("#PassportNumber").val();

  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/IsApplicationAvailableByPersonalInformation",
      data: {
        name: name,
        surname: surname,
        birthDate: birthDate,
        maidenName: maidenName,
        fatherName: fatherName,
        motherName: motherName,
        countryId: countryId,
        nationalityId: nationalityId,
        passportNumber: passportNumber,
        encryptedApplicationId: encryptedApplicationId,
        isAllowDeniedPassport: isAllowDeniedPassport,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            //
          } else if (data.ErrorMessage) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                data.ErrorMessage +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
      },
      complete: function () {
        resolve();
      },
    });
  });
}

function checkPreviousJob() {
  var passportNumber = $("#PassportNumber").val();
  var name = $("#Name").val();
  var surname = $("#Surname").val();
  var birthDate = $("#BirthDate").val();
  var fatherName = $("#FatherName").val();
  var motherName = $("#MotherName").val();
  var countryId = $("#CountryId").val();
  var nationalityId = $("#NationalityId").val();
  var job = $("#Job").val();
  var oppucationId = $("#OccupationId").val();

  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/IsApplicationHasDifferentJob",
      data: {
        passportNumber: passportNumber,
        countryId: countryId,
        nationalityId: nationalityId,
        job: job,
        oppucationId: oppucationId,
        name: name,
        surname: surname,
        birthDate: birthDate,
        fatherName: fatherName,
        motherName: motherName,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            //
          } else if (data.ErrorMessage) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                data.ErrorMessage +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
      },
      complete: function () {
        resolve();
      },
    });
  });
}

function checkPreviousNote() {
  var passportNumber = $("#PassportNumber").val();
  var countryId = $("#CountryId").val();
  var nationalityId = $("#NationalityId").val();

  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/IsApplicationHasPreviousNote",
      data: {
        passportNumber: passportNumber,
        countryId: countryId,
        nationalityId: nationalityId,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            //
          } else if (data.ApplicationNote) {
            $("#ApplicationPreviousNote-Window")
              .data("kendoWindow")
              .content(data.ApplicationNote);
            $("#ApplicationPreviousNote-Window").data("kendoWindow").open();
            $("#ApplicationPreviousNote-Window").data("kendoWindow").center();

            if (data.CancelledInsuranceMessage) {
              showNotification("warning", data.CancelledInsuranceMessage);
            }
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
      },
      complete: function () {
        resolve();
      },
    });
  });
}

function checkRejectedApplicationsByPhoneNumbers() {
  var phoneNumber1 = $("#PhoneNumber1").val();
  var phoneNumber2 = $("#PhoneNumber2").val();
  var countryCallingCode = $("#CountryCallingCode").val();
  var nationalityId = $("#NationalityId").val();
  var countryId = $("#CountryId").val();

  return new Promise(function (resolve, reject) {
    $.ajax({
      type: "GET",
      url: "/Appointment/Application/GetRejectedApplicationsByPhones",
      data: {
        phoneNumber1: phoneNumber1,
        phoneNumber2: phoneNumber2,
        countryCallingCode: countryCallingCode,
        nationalityId: nationalityId,
        countryId: countryId,
      },
      success: function (data) {
        if (data) {
          if (data.Result === true && data.HasRejected === false) {
            // No rejected applications found, continue
          } else if (
            data.Result === true &&
            data.HasRejected === true &&
            data.Html
          ) {
            // Show warning and popup with rejected applications
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                jsResources.PreviousRejectedApplicationsFoundByPhone +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");

            // Show popup with rejected applications list
            $("#PhoneRejectedApplications-Window")
              .data("kendoWindow")
              .content(data.Html);
            $("#PhoneRejectedApplications-Window").data("kendoWindow").open();
            $("#PhoneRejectedApplications-Window").data("kendoWindow").center();

            isFormValid &= false;
          } else if (data.ErrorMessage) {
            bootbox.dialog({
              message:
                '<p style="color:#FFFFFF;"><b>' +
                data.ErrorMessage +
                "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
            $(".modal-content").css("background-color", "#FFA800");
            isFormValid &= false;
          } else {
            showNotification("danger", jsResources.ErrorOccurred);
            isFormValid &= false;
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
        isFormValid &= false;
      },
      complete: function () {
        resolve();
      },
    });
  });
}

$(document).ready(function () {
  var original = $("#ApplicationPreviousNote-Window").clone(true);

  $(".box-col input").change(function () {
    var clone = original.clone(true);

    $("#ApplicationPreviousNote-Window").data("kendoWindow").destroy();

    setTimeout(function () {
      $("#ApplicationPreviousNote").append(clone);
      initWindow();
    }, 200);
  });

  var getEffects = function () {
    return "expand:vertical " + "fadeIn";
  };

  function initWindow() {
    var windowOptions = {
      actions: ["Close"],
      draggable: true,
      resizable: true,
      title: jsResources.PreviousApplications,
      visible: false,
    };

    windowOptions.animation = {
      open: { effects: getEffects() },
      close: { effects: getEffects(), reverse: true },
    };

    $("#ApplicationPreviousNote-Window").kendoWindow(windowOptions);
  }

  initWindow();
});

// Initialize Phone Rejected Applications Window
$(document).ready(function () {
  var original = $("#PhoneRejectedApplications-Window").clone(true);

  $(".box-col input").change(function () {
    var clone = original.clone(true);

    $("#PhoneRejectedApplications-Window").data("kendoWindow").destroy();

    setTimeout(function () {
      $("#PhoneRejectedApplications").append(clone);
      initPhoneRejectedWindow();
    }, 200);
  });

  var getEffects = function () {
    return "expand:vertical " + "fadeIn";
  };

  function initPhoneRejectedWindow() {
    var windowOptions = {
      actions: ["Close"],
      draggable: true,
      resizable: true,
      title:
        jsResources.RejectedApplicationsByPhone ||
        "Rejected Applications by Phone",
      visible: false,
      width: 800,
      height: 600,
    };

    windowOptions.animation = {
      open: { effects: getEffects() },
      close: { effects: getEffects(), reverse: true },
    };

    $("#PhoneRejectedApplications-Window").kendoWindow(windowOptions);
  }

  initPhoneRejectedWindow();
});

function checkExtraFees() {
  let applicationType = $("#ApplicationTypeId").val(); // ApplicationTypeId=10 NonApplicationPhotograph
  let branchCountryId = $("#BranchCountryId").val();
  let photoStaffCheck = false;
  $(extraFees).each(function (i) {
    let index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
    let extraFeeName = $("#ExtraFees_" + index + "__ExtraFeeName").val();
    let extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
    let categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
    if (applicationType != 11 && categoryTypeId == 7) {
      //AdditionalServiceTypeId=4 YSS
      $(extraFees[i]).prop("disabled", true);
    }

    if (extraFeeId == 67) {
      if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
        photoStaffCheck = true;
      }
    }
    if (
      !photoStaffCheck &&
      extraFeeId != 67 &&
      (extraFeeName.includes("Personel") || extraFeeName.includes("Staff"))
    ) {
      $(extraFees[i]).prop("disabled", true);
    }
  });
}

function checkExtraFeesForVisaCategory() {
  let isExist3MontInsurance = false;
  $(extraFees).each(function (i) {
    let index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
    let extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
    if (
      extraFeeId == 11 ||
      extraFeeId == 27 ||
      extraFeeId == 52 ||
      extraFeeId == 140
    ) {
      isExist3MontInsurance = true;
    }
  });
  if (isExist3MontInsurance) {
    let visaCategoryType = $("#VisaCategoryId").val(); // VisaCategory Work Permit
    let applicationTypeId = $("#ApplicationTypeId").val();
    $(extraFees).each(function (i) {
      let index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
      let extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
      let categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
      if (
        (applicationTypeId == 1 ||
          applicationTypeId == 5 ||
          applicationTypeId == 6 ||
          applicationTypeId == 7 ||
          applicationTypeId == 12) &&
        visaCategoryType == 7 &&
        categoryTypeId == 2 &&
        !(
          extraFeeId == 11 ||
          extraFeeId == 27 ||
          extraFeeId == 52 ||
          extraFeeId == 140
        )
      ) {
        $(extraFees[i]).prop("checked", false);
        $(extraFees[i]).prop("disabled", true);
      } else if (
        applicationTypeId != 11 &&
        (applicationTypeId != 1 ||
          applicationTypeId != 5 ||
          applicationTypeId != 6 ||
          applicationTypeId != 7 ||
          applicationTypeId != 12 ||
          visaCategoryType != 7) &&
        categoryTypeId == 2 &&
        !(
          extraFeeId == 11 ||
          extraFeeId == 27 ||
          extraFeeId == 52 ||
          extraFeeId == 140
        )
      ) {
        $(extraFees[i]).prop("disabled", false);
      }
    });
  }
}

function checkExtraFeesForNormalApplicant(index) {
  let extraFeeId = 0;
  let initialIndex = index;
  if (index == -1) {
    $(extraFees).each(function (i) {
      let index_ = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
      let extraFeeId_ = $("#ExtraFees_" + index_ + "__ExtraFeeId").val();
      if (extraFeeId_ == 7) {
        index = index_;
        extraFeeId = extraFeeId_;
      }
    });
  } else {
    extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
  }

  if (
    ($("#ApplicationTypeId").val() == 1 ||
      $("#ApplicationTypeId").val() == 5 ||
      $("#ApplicationTypeId").val() == 6 ||
      $("#ApplicationTypeId").val() == 7) &&
    extraFeeId == 7 &&
    ($("#CountryCallingCode").val() == "213" ||
      $("#CountryCallingCode").val() == "965" ||
      $("#CountryCallingCode").val() == "7" ||
      $("#CountryCallingCode").val() == "91" ||
      $("#CountryCallingCode").val() ==
        "966") /* || $("#CountryCallingCode").val() == "977" || $("#CountryCallingCode").val() == "971"*/
  ) {
    // Normal Applicant,Turquois,Turquois Premium,Turquois Gratis and VIP and Algeria,Kuwait,Russia,India,Saudi Arabia,Nepal,United Arab Emirates
    if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
      // VIP
      $(extraFees).each(function (i) {
        let index2 = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId2 = $("#ExtraFees_" + index2 + "__ExtraFeeId").val();
        if (extraFeeId2 == 9) {
          //SMS
          $(extraFees[i]).prop("checked", false);
          $(extraFees[i]).prop("disabled", true);
          $(".paymentShowHide_" + index2).hide();
        } else if (extraFeeId2 == 40) {
          //SMS VIP
          $(extraFees[i]).prop("checked", true);
          if ($("#ShowPaymentMethods").val() == "True") {
            $(".paymentShowHide_" + index2).show();
          }
        } else if (
          extraFeeId2 == 30 &&
          $("#CountryCallingCode").val() == "91"
        ) {
          // ExtraFee: 30 -> Cargo, CallingCode: 91 -> India
          $(extraFees[i]).prop("disabled", true);
          $(extraFees[i]).prop("checked", false);
          if ($("#ShowPaymentMethods").val() == "True") {
            $(".paymentShowHide_" + index2).hide();
          }
        }
      });
    } else {
      $(extraFees).each(function (i) {
        let index2 = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId2 = $("#ExtraFees_" + index2 + "__ExtraFeeId").val();
        if (extraFeeId2 == 9) {
          //SMS
          $(extraFees[i]).prop("disabled", false);
        }
        if (initialIndex != -1 && extraFeeId2 == 40) {
          //SMS VIP
          $(extraFees[i]).prop("checked", false);
          $(".paymentShowHide_" + index2).hide();
        }
        if (extraFeeId2 == 30 && $("#CountryCallingCode").val() == "91") {
          // Cargo
          $(extraFees[i]).prop("disabled", false);
        }
        if (
          initialIndex != -1 &&
          extraFeeId2 == 40 &&
          $("#CountryCallingCode").val() == "91"
        ) {
          //Cargo VIP
          $(extraFees[i]).prop("checked", false);
          $(".paymentShowHide_" + index2).hide();
        }
      });
    }
  } else if (
    ($("#ApplicationTypeId").val() == 1 ||
      $("#ApplicationTypeId").val() == 5 ||
      $("#ApplicationTypeId").val() == 6 ||
      $("#ApplicationTypeId").val() == 7) &&
    extraFeeId == 7 &&
    $("#CountryCallingCode").val() == "964"
  ) {
    // Normal Applicant,Turquois,Turquois Premium,Turquois Gratis and VIP and Iraq
    if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
      // VIP
      $(extraFees).each(function (i) {
        let index2 = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId2 = $("#ExtraFees_" + index2 + "__ExtraFeeId").val();
        if (extraFeeId2 == 186) {
          //Optional SMS
          $(extraFees[i]).prop("checked", false);
          $(extraFees[i]).prop("disabled", true);
          $(".paymentShowHide_" + index2).hide();
        } else if (extraFeeId2 == 187) {
          //Optional SMS VIP
          $(extraFees[i]).prop("checked", true);
          if ($("#ShowPaymentMethods").val() == "True") {
            $(".paymentShowHide_" + index2).show();
          }
        }
      });
    } else {
      $(extraFees).each(function (i) {
        let index2 = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId2 = $("#ExtraFees_" + index2 + "__ExtraFeeId").val();
        if (extraFeeId2 == 186) {
          //Optional SMS
          $(extraFees[i]).prop("disabled", false);
        }
        if (initialIndex != -1 && extraFeeId2 == 187) {
          //Optional SMS VIP
          $(extraFees[i]).prop("checked", false);
          $(".paymentShowHide_" + index2).hide();
        }
      });
    }
  }

  if (extraFeeId == 67) {
    //Gateway Servis Ücreti (Personel)
    if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
      $(extraFees).each(function (i) {
        let index2 = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId = $("#ExtraFees_" + index2 + "__ExtraFeeId").val();
        var extraFeeName = $("#ExtraFees_" + index2 + "__ExtraFeeName").val();
        if (
          (extraFeeName.includes("Personel") ||
            extraFeeName.includes("Staf")) &&
          extraFeeId != 67
        ) {
          $(extraFees[i]).prop("disabled", false);
        }
      });
    } else {
      $(extraFees).each(function (i) {
        let index2 = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        let extraFeeId = $("#ExtraFees_" + index2 + "__ExtraFeeId").val();
        var extraFeeName = $("#ExtraFees_" + index2 + "__ExtraFeeName").val();
        if (
          (extraFeeName.includes("Personel") ||
            extraFeeName.includes("Staf")) &&
          extraFeeId != 67
        ) {
          $(extraFees[i]).prop("checked", false);
          $(extraFees[i]).prop("disabled", true);
        }
      });
    }
  }
}

function displayApplicationSummary() {
  var parentItem = $("#divApplicationSummary");

  var agencyName = document.querySelectorAll(
    "[aria-owns='AgencyId_listbox']"
  )[0].innerText;

  parentItem.empty();

  var dynamicHtml = '<table class="table table-bordered text-center">';
  dynamicHtml += "<tbody>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td colspan="2" class="h6 text-left">' +
    jsResources.ApplicationStep1Description +
    "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.ApplicantType + ":</td>";
  dynamicHtml +=
    "<td>" +
    getItemTextValue($("#ApplicantTypeId").val(), "GetApplicantType") +
    "</td>";
  dynamicHtml += "</tr>";

  if (agencyName !== jsResources.Select && !$("#IsAgency").is(":checked")) {
    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.Agency + ":</td>";
    dynamicHtml += "<td>" + agencyName + "</td>";
    dynamicHtml += "</tr>";
  }

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.ApplicationType + ":</td>";
  dynamicHtml +=
    "<td>" +
    getItemTextValue($("#ApplicationTypeId").val(), "GetApplicationType") +
    "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' +
    jsResources.ApplicationPassportStatus +
    ":</td>";
  dynamicHtml +=
    "<td>" +
    getItemTextValue(
      $("#ApplicationPassportStatusId").val(),
      "GetApplicationPassportStatus"
    ) +
    "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.PassportNumber + ":</td>";
  dynamicHtml += "<td>" + $("#PassportNumber").val() + "</td>";
  dynamicHtml += "</tr>";

  if ($("#CountryCallingCode").val() == "966") {
    //Saudi Arabia
    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.ResidenceNumber +
      ":</td>";
    dynamicHtml += "<td>" + $("#ResidenceNumber").val() + "</td>";
    dynamicHtml += "</tr>";
  }

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' +
    jsResources.PassportExpireDate +
    ":</td>";
  if ($("#PassportExpireDate").val() != "")
    dynamicHtml += "<td>" + $("#PassportExpireDate").val() + "</td>";
  else dynamicHtml += "<td>" + "N/A" + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td colspan="2" class="h6 text-left">' +
    jsResources.ApplicationStep2BasicInformation +
    "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Title + ":</td>";
  dynamicHtml +=
    "<td>" + getItemTextValue($("#TitleId").val(), "GetTitle") + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Name + ":</td>";
  dynamicHtml += "<td>" + $("#Name").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Surname + ":</td>";
  dynamicHtml += "<td>" + $("#Surname").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.BirthDate + ":</td>";
  dynamicHtml += "<td>" + $("#BirthDate").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Gender + ":</td>";
  dynamicHtml +=
    "<td>" + getItemTextValue($("#GenderId").val(), "GetGender") + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.MaritalStatus + ":</td>";
  if ($("#MaritalStatusId").val() != "")
    dynamicHtml +=
      "<td>" +
      getItemTextValue($("#MaritalStatusId").val(), "GetMaritalStatus") +
      "</td>";
  else dynamicHtml += "<td>" + "N/A" + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Nationality + ":</td>";
  dynamicHtml +=
    "<td>" +
    getItemTextValue($("#NationalityId").val(), "GetNationality") +
    "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.MaidenName + ":</td>";
  dynamicHtml += "<td>" + $("#MaidenName").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.FatherName + ":</td>";
  dynamicHtml += "<td>" + $("#FatherName").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.MotherName + ":</td>";
  dynamicHtml += "<td>" + $("#MotherName").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Email + ":</td>";
  dynamicHtml += "<td>" + $("#Email").val() + "</td>";
  dynamicHtml += "</tr>";

  var phoneNumber1CountryCallingCode = "";
  if ($("#PhoneNumber1").val())
    phoneNumber1CountryCallingCode = "+" + $("#CountryCallingCode").val();

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.PhoneNumber + " #1 :</td>";
  dynamicHtml +=
    "<td>" +
    phoneNumber1CountryCallingCode +
    $("#PhoneNumber1").val() +
    "</td>";
  dynamicHtml += "</tr>";

  var phoneNumber2CountryCallingCode = "";
  if ($("#PhoneNumber2").val())
    phoneNumber2CountryCallingCode = "+" + $("#CountryCallingCode").val();

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.PhoneNumber + " #2 :</td>";
  dynamicHtml +=
    "<td>" +
    phoneNumber2CountryCallingCode +
    $("#PhoneNumber2").val() +
    "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.Address + ":</td>";
  dynamicHtml += "<td>" + $("#Address").val() + "</td>";
  dynamicHtml += "</tr>";

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.City + ":</td>";
  dynamicHtml +=
    "<td>" +
    getItemTextValue($("#ForeignCityId").val(), "GetForeignCity") +
    "</td>";
  dynamicHtml += "</tr>";

  if ($("#PostalCode").val()) {
    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.PostalCode + ":</td>";
    dynamicHtml += "<td>" + $("#PostalCode").val() + "</td>";
    dynamicHtml += "</tr>";
  }

  if ($("#NameOfSecondContactPerson").val()) {
    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.NameOfSecondContactPerson +
      ":</td>";
    dynamicHtml += "<td>" + $("#NameOfSecondContactPerson").val() + "</td>";
    dynamicHtml += "</tr>";
  }

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td class="font-weight-bolder">' + jsResources.AreaName + ":</td>";
  dynamicHtml += "<td>" + $("#AreaName").val() + "</td>";
  dynamicHtml += "</tr>";

  var isApplicationDocumentIgnored = $("#IsApplicationDocumentIgnored").val();

  if (isApplicationDocumentIgnored === "true") {
    //
  } else {
    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td colspan="2" class="h6 text-left">' +
      jsResources.ApplicationStep3DocumentInformation +
      "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.TotalYearInCountry +
      ":</td>";
    dynamicHtml += "<td>" + $("#TotalYearInCountry").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.ReimbursementType +
      ":</td>";
    dynamicHtml +=
      "<td>" +
      getItemTextValue(
        $("#ReimbursementTypeId").val(),
        "GetReimbursementType"
      ) +
      "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.ReimbursementSponsorDetail +
      ":</td>";
    dynamicHtml += "<td>" + $("#ReimbursementSponsorDetail").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.Job + ":</td>";
    var Job =
      $("#Job").val() != String.empty
        ? $("#Job").val()
        : getItemTextValue($("#OccupationId").val(), "GetDataOccupationType");
    dynamicHtml += "<td>" + Job + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.CompanyName + ":</td>";
    dynamicHtml += "<td>" + $("#CompanyName").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.TotalYearInCompany +
      ":</td>";
    dynamicHtml += "<td>" + $("#TotalYearInCompany").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.MonthlySalary + ":</td>";
    dynamicHtml +=
      "<td>" +
      $("#MonthlySalary").val() +
      " " +
      getItemTextValue($("#MonthlySalaryCurrencyId").val(), "GetCurrencyType") +
      "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.HasBankAccount + ":</td>";
    var hasBankAccount = $("#HasBankAccount").is(":checked")
      ? jsResources.Yes
      : jsResources.No;
    dynamicHtml += "<td>" + hasBankAccount + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.BankBalance + ":</td>";
    dynamicHtml +=
      "<td>" +
      $("#BankBalance").val() +
      " " +
      getItemTextValue($("#BankBalanceCurrencyId").val(), "GetCurrencyType") +
      "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.HasDeed + ":</td>";
    var hasDeed = $("#HasDeed").is(":checked")
      ? jsResources.Yes
      : jsResources.No;
    dynamicHtml += "<td>" + hasDeed + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.VisaCategory + ":</td>";
    dynamicHtml +=
      "<td>" +
      getItemTextValue($("#VisaCategoryId").val(), "GetVisaCategoryType") +
      "</td>";
    dynamicHtml += "</tr>";

    if ($("#CountryCallingCode").val() == "966") {
      // Saudi Arabia
      dynamicHtml += "<tr>";
      dynamicHtml +=
        '<td class="font-weight-bolder">' +
        jsResources.NumberOfEntry +
        ":</td>";
      dynamicHtml +=
        "<td>" +
        getItemTextValue($("#NumberOfEntryId").val(), "GetNumberOfEntryType") +
        "</td>";
      dynamicHtml += "</tr>";
    }

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.HasEntryBan + ":</td>";
    var hasEntryBan = $("#HasEntryBan").is(":checked")
      ? jsResources.Yes
      : jsResources.No;
    dynamicHtml += "<td>" + hasEntryBan + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.EntryDate + ":</td>";
    dynamicHtml += "<td>" + $("#EntryDate").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.ExitDate + ":</td>";
    dynamicHtml += "<td>" + $("#ExitDate").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.CityToVisit + ":</td>";
    dynamicHtml += "<td>" + $("#CityName").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.AccomodationDetail +
      ":</td>";
    dynamicHtml += "<td>" + $("#AccomodationDetail").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.HasRelativeAbroad +
      ":</td>";
    var hasRelativeAbroad = $("#HasRelativeAbroad").is(":checked")
      ? jsResources.Yes
      : jsResources.No;
    dynamicHtml += "<td>" + hasRelativeAbroad + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.RelativeLocation +
      ":</td>";
    dynamicHtml += "<td>" + $("#RelativeLocation").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.ResidenceApplicationToBeMade +
      ":</td>";
    let ResidenceApplicationToBeMade =
      $("#ResidenceApplication").val() == "1"
        ? jsResources.Yes
        : $("#ResidenceApplication").val() == "2"
        ? jsResources.No
        : jsResources.Unspecified;
    dynamicHtml += "<td>" + ResidenceApplicationToBeMade + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.ApplicationTogether +
      ":</td>";
    let applicationTogether =
      $("#ApplicationTogetherId").val() == 1 ? jsResources.Yes : jsResources.No;
    dynamicHtml += "<td>" + applicationTogether + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      "50 " +
      jsResources.Year +
      " +" +
      ":</td>";
    dynamicHtml +=
      "<td>" + $("#ApplicationTogetherFiftyYearCount").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      "15 " +
      jsResources.Year +
      " -" +
      ":</td>";
    dynamicHtml +=
      "<td>" + $("#ApplicationTogetherFifteenYearCount").val() + "</td>";
    dynamicHtml += "</tr>";

    if ($("#CountryCallingCode").val() == "993") {
      // Turkmenistan
      dynamicHtml += "<tr>";
      dynamicHtml +=
        '<td class="font-weight-bolder">' +
        jsResources.HasPersonVisitedTurkeyBefore +
        ":</td>";
      var hasPersonVisitedTurkeyBefore = $("#HasPersonVisitedTurkeyBefore").is(
        ":checked"
      )
        ? jsResources.Yes
        : jsResources.No;
      dynamicHtml += "<td>" + hasPersonVisitedTurkeyBefore + "</td>";
      dynamicHtml += "</tr>";
    }

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.PersonTravelWith +
      ":</td>";
    dynamicHtml += "<td>" + $("#PersonTravelWith").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' +
      jsResources.PersonTravelWithHasVisa +
      ":</td>";
    var personTravelWithHasVisa = $("#PersonTravelWithHasVisa").is(":checked")
      ? jsResources.Yes
      : jsResources.No;
    dynamicHtml += "<td>" + personTravelWithHasVisa + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.VehicleType + ":</td>";
    dynamicHtml += "<td>" + $("#VehicleType").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.PlateNo + ":</td>";
    dynamicHtml += "<td>" + $("#PlateNo").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.ModelYear + ":</td>";
    dynamicHtml += "<td>" + $("#ModelYear").val() + "</td>";
    dynamicHtml += "</tr>";

    dynamicHtml += "<tr>";
    dynamicHtml +=
      '<td class="font-weight-bolder">' + jsResources.ChassisNumber + ":</td>";
    dynamicHtml += "<td>" + $("#ChassisNumber").val() + "</td>";
    dynamicHtml += "</tr>";
  }

  dynamicHtml += "<tr>";
  dynamicHtml +=
    '<td colspan="2" class="h6 text-left">' +
    jsResources.ApplicationStep4ExtraPackages +
    "</td>";
  dynamicHtml += "</tr>";

  var extraFeeInputs = $("input[id*='ExtraFees_']");
  var extraFees = [];

  $(extraFeeInputs).each(function (i) {
    if (extraFeeInputs[i].id.includes("IsChecked")) {
      extraFees.push(extraFeeInputs[i]);
    }
  });

  $(extraFees).each(function (i) {
    if (extraFees[i].id.includes("IsChecked")) {
      var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
      if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
        var peymentMethod = $("#ExtraFees_" + index + "__Cash").is(":checked")
          ? " (" + jsResources.Cash + ")"
          : $("#ExtraFees_" + index + "__Pos").is(":checked")
          ? " (Pos)"
          : $("#ExtraFees_" + index + "__Online").is(":checked")
          ? " (Online)"
          : "";
        dynamicHtml += "<tr>";
        dynamicHtml +=
          '<td class="font-weight-bolder">' +
          $(extraFees[i]).parent().text() +
          peymentMethod +
          "</td>";
        dynamicHtml +=
          "<td>" +
          $("#ExtraFees_" + index + "__Quantity").val() +
          " " +
          jsResources.Item +
          "</td>";
        dynamicHtml += "</tr>";
      }
    }
  });

  dynamicHtml += "</tbody>";
  dynamicHtml += "</table>";

  parentItem.append(dynamicHtml);

  $("#divApplicationSummary tr").each(function () {
    if ($(this).children().length === 2) {
      if (!$.trim($(this).children()[1].innerHTML).length) $(this).hide();
    }
  });
}

function getApplicationHistoryByPassportNumberAndNationality() {
  var passportNumber = $("#PassportNumber").val();
  var nationalityId = $("#NationalityId").val();
  var countryId = $("#CountryId").val();

  if (!passportNumber || !nationalityId || !countryId) {
    showNotification("danger", jsResources.ErrorOccurred);
    $("#modalApplicationHistoryByPassportNumber").modal("hide");
    return;
  }

  $.post(
    "/Appointment/Application/PartialApplicationHistoryByPassportNumber",
    {
      passportNumber: passportNumber,
      countryId: countryId,
      IngonreCancelledApplications: false,
    },
    function (data) {
      $("#divPartialApplicationHistoryByPassportNumber").html(data);
    },
    "html"
  );
}

function partialApplicationHistoryByPassportNumber() {
  var passportNumber = $("#PassportNumber").val();
  var countryId = $("#CountryId").val();
  var encryptedApplicationId = $("#EncryptedApplicationId").val();

  $.post(
    "/Appointment/Application/PartialApplicationHistoryByPassportNumber",
    {
      passportNumber: passportNumber,
      countryId: countryId,
      encryptedApplicationId: encryptedApplicationId,
      IngonreCancelledApplications: false,
    },
    function (data) {
      $("#divPartialApplicationHistoryByPassportNumber").html(data);
    },
    "html"
  );
}

function importApplicationDataFromHistory(encryptedApplicationId) {
  $.ajax({
    type: "GET",
    url: "/Appointment/Application/ImportApplicationDataFromHistory",
    data: { encryptedApplicationId },
    success: function (result) {
      if (result.ResultType !== 1) {
        showNotification("danger", jsResources.ErrorOccurred);
        return;
      }

      const formData = result.Data;

      // Process main form data (excluding VisaHistories)
      Object.entries(formData).forEach(([key, value]) => {
        if (value === null || value === undefined || key === "VisaHistories")
          return;
        setFormElementValue(key, value);
      });

      // Process VisaHistories if they exist
      if (
        Array.isArray(formData.VisaHistories) &&
        formData.VisaHistories.length > 0
      ) {
        processVisaHistories(formData.VisaHistories);
      }

      showNotification(
        "success",
        jsResources.ImportApplicationDataSuccessfullMessage
      );
      $("#modalApplicationHistoryByPassportNumber").modal("hide");
    },
    error: function (xhr, status, error) {
      console.error("Import failed:", status, error);
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
}

function processVisaHistories(visaHistories) {
  const visaFields = [
    "CountryId",
    "FromDate",
    "UntilDate",
    "IsUsed",
    "NumberOfEntryId",
    "VisaIsUsedYear",
    "OldVisaDecisionId",
  ];

  visaHistories.forEach((visaHistory, index) => {
    if (!visaHistory) return;

    visaFields.forEach((field) => {
      if (visaHistory[field] !== undefined) {
        setFormElementValue(
          `VisaHistories_${index}__${field}`,
          visaHistory[field]
        );
      }
    });
  });
}

// Sets the value of a form element based on its type and attributes
function setFormElementValue(elementId, value, skipHidden = true) {
  const $element = $(`#${elementId}`);
  if (!$element.length) return;

  // Skip hidden input fields if specified
  if (skipHidden && $element.attr("type") === "hidden") return;

  const excludedFields = ["EntryDate", "ExitDate", "HasNoEmail"];
  if (excludedFields.includes(elementId)) return;

  // Handle different input types
  if ($element.attr("data-role") === "datepicker") {
    handleDatepicker($element, value);
  } else if (
    $element.hasClass("k-dropdown") ||
    $element.data("kendoDropDownList")
  ) {
    handleDropdown($element, value, elementId);
  } else if ($element.is(":checkbox")) {
    $element.prop("checked", value).trigger("change");
  } else if ($element.attr("data-role") === "numerictextbox") {
    var numerictextbox = $element.data("kendoNumericTextBox");
    if (numerictextbox) {
      numerictextbox.value(value);
      numerictextbox.trigger("change");
      $element.trigger("change");
    }
  } else {
    // Default: set value directly
    $element.val(value).trigger("change");
  }
}
function handleDatepicker($element, value) {
  if (!value) return;

  try {
    const parsedDate = new Date(value);
    if (isNaN(parsedDate.getTime())) return; // Invalid date

    const formattedDate = `${parsedDate
      .getDate()
      .toString()
      .padStart(2, "0")}/${(parsedDate.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${parsedDate.getFullYear()}`;

    const datePicker = $element.data("kendoDatePicker");
    if (datePicker) {
      datePicker.value(formattedDate);
    } else {
      $element.val(formattedDate);
    }
  } catch (e) {
    console.error("Error formatting date:", e);
  }
}
function handleDropdown($element, value, elementId) {
  try {
    const dropdown = $element.data("kendoDropDownList");
    if (dropdown) {
      dropdown.value(value);
      dropdown.trigger("change");
      handleSpecialCases(elementId);
    } else {
      $element.val(value).trigger("change");
    }
  } catch (error) {
    console.error("Error in handleDropdown:", error, elementId);
  }
}

function handleSpecialCases(elementId) {
  const specialHandlers = {
    ApplicationTogetherId: () => {
      checkPersonTravelWithVisibility();
      checkRelatedInsuranceTypeExtraFee();
    },
    ProvidedWithHasRelatedInsuranceId: () => {
      checkReleatedInvidualInsuranceVisibility();
    },
  };

  const handler = specialHandlers[elementId];
  if (handler) {
    try {
      handler();
    } catch (error) {
      console.error(`Error handling special case for ${elementId}:`, error);
    }
  }
}

function partialGetPreApplicationHistory() {
  var passportNumber = $("#PassportNumber").val();
  var nationalityId = $("#NationalityId").val();

  if (passportNumber === undefined || passportNumber === "") {
    bootbox.alert({
      title: jsResources.Warning,
      message: jsResources.EnterPassportNumberPlease,
      callback: function (result) {},
    });
  } else {
    $.post(
      "/Appointment/Application/PartialGetPreApplicationHistory",
      {
        passportNumber: passportNumber,
        nationalityId: nationalityId,
      },
      function (data) {
        $("#modalGetPreApplicationHistory").modal("show");
        $("#divGetPreApplicationHistory").html(data);
      },
      "html"
    );
  }
}

function partialGetPreApplicationHistory() {
  var passportNumber = $("#PassportNumber").val();
  var nationalityId = $("#NationalityId").val();

  if (passportNumber === undefined || passportNumber === "") {
    bootbox.alert({
      title: jsResources.Warning,
      message: jsResources.EnterPassportNumberPlease,
      callback: function (result) {},
    });
  } else {
    $.post(
      "/Appointment/Application/PartialGetPreApplicationHistory",
      {
        passportNumber: passportNumber,
        nationalityId: nationalityId,
      },
      function (data) {
        $("#modalGetPreApplicationHistory").modal("show");
        $("#divGetPreApplicationHistory").html(data);
      },
      "html"
    );
  }
}

function goToNewStep(wizard) {
  if (isFormValid) {
    if (wizard.getNewStep() > 3) {
      if (!checkPassportValidityPeriodForVisaType()) {
        bootbox.dialog({
          message:
            '<p style="color:#FFFFFF;"><b>' +
            jsResources.PassportValidityPeriodForVisaTypeWarning +
            "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
        $(".modal-content").css("background-color", "#FFA800");
        isFormValid &= false;
        wizard.goTo(
          wizard.getNewStep() === 4
            ? wizard.getNewStep() - 1
            : wizard.getNewStep() - 2
        );
        KTUtil.scrollTop();
        return;
      }
    }

    var isApplicationDocumentIgnored = $("#IsApplicationDocumentIgnored").val();

    if (isApplicationDocumentIgnored === "true" && wizard.getNewStep() === 3) {
      wizard.goTo(wizard.getNewStep() + 1);
      KTUtil.scrollTop();
    } else {
      if (wizard.getNewStep() === wizard.totalSteps) {
        displayApplicationSummary();
      }
      wizard.goTo(wizard.getNewStep());
      KTUtil.scrollTop();
    }
  }
}

function checkBirthDate() {
  var birthDate = kendo.parseDate(
    $("#BirthDate").val(),
    jsResources.DatePickerFormatJs
  );
  if (birthDate == null) return false;

  var todayDate = new Date(new Date().toDateString());
  var diff = new Date(todayDate - birthDate);
  var days = diff / 1000 / 60 / 60 / 24;
  if (days < 0) return false;

  return true;
}

function checkExtraPackageUpdateAllowed() {
  var isExistingApplication = $("#IsExistingApplication").val();
  var applicationTypeId = parseInt($("#ApplicationTypeId").val());

  // ApplicationType.NonApplicationRelatedInsurance = 14
  if (applicationTypeId === 14) {
    $("#AllowUpdate").prop("checked", false).prop("disabled", true);
    $("#divExtraPackageDetails :input").attr("onclick", "return false;");
    $("#divExtraPackageDetails :input").attr("readonly", true);
    return;
  }

  if (
    isExistingApplication === "false" ||
    isExistingApplication === "False" ||
    isExistingApplication === false
  ) {
    //continue
  } else {
    var allowUpdate = $("#AllowUpdate").is(":checked");
    $("#divExtraPackageDetails :input").attr("onclick", "return false;");
    $("#divExtraPackageDetails :input").attr("readonly", true);
    $("#AllowUpdate").prop("checked", false);

    if (allowUpdate && applicationTypeId !== 14) {
      return new Promise(function (resolve, reject) {
        $.ajax({
          type: "GET",
          url: "/Appointment/Application/IsExtraPackageUpdateAllowed",
          data: {},
          success: function (data) {
            if (data) {
              if (data.Result === true) {
                $("#divExtraPackageDetails :input").removeAttr("onclick");
                $("#divExtraPackageDetails :input").removeAttr("readonly");
                $("#AllowUpdate").prop("checked", true);
              } else if (data.Message) {
                bootbox.dialog({
                  message:
                    '<p style="color:#FFFFFF;"><b>' + data.Message + "</b></p>",
                  size: "extra-large",
                  onEscape: true,
                  backdrop: true,
                });
                $(".modal-content").css("background-color", "#FFA800");
              } else {
                showNotification("danger", jsResources.ErrorOccurred);
              }
            }
          },
          error: function () {
            showNotification("danger", jsResources.ErrorOccurred);
          },
          complete: function () {
            resolve();
          },
        });
      });
    }
  }
}

function checkContactInformationUpdateAllowed() {
  var isApplicationUpdateStatusCheckActive = $(
    "#IsApplicationUpdateStatusCheckActive"
  ).val();
  var isExistingApplication = $("#IsExistingApplication").val();
  var applicationId = $("#EncryptedApplicationId").val();

  if (
    isExistingApplication === "false" ||
    isExistingApplication === "False" ||
    isExistingApplication === false
  ) {
    //continue
  } else {
    if (
      isApplicationUpdateStatusCheckActive === "true" ||
      isApplicationUpdateStatusCheckActive === "True" ||
      isApplicationUpdateStatusCheckActive === true
    ) {
      return new Promise(function (resolve, reject) {
        $.ajax({
          type: "GET",
          url: "/Appointment/Application/CheckContactInformationUpdateAllowed",
          data: {
            encryptedApplicationId: applicationId,
          },
          success: function (data) {
            if (data) {
              if (data.Data.Result === true) {
                $("#divEmail :input").attr("readonly", true);
                $("#divPhone1 :input").attr("readonly", true);
                $("#divPhone2 :input").attr("readonly", true);
              }
            }
          },
          error: function () {
            showNotification("danger", jsResources.ErrorOccurred);
          },
          complete: function () {
            resolve();
          },
        });
      });
    }
  }
}

$("#PassportNumber").on("change", function () {
  onPassportNumberChange();
});

function onPassportNumberChange() {
  if (
    $("#PassportNumber").val() != null &&
    $("#NationalityId").val() != null &&
    !checkNll
  ) {
    checkNll = true;
    $.ajax({
      type: "GET",
      async: false,
      url: "/Appointment/Application/GetNllChechByPassportNumberAndNationalty",
      data: {
        nationalityId: $("#NationalityId").val(),
        passportNumber: $("#PassportNumber").val(),
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            bootbox.dialog({
              title: jsResources.Information,
              message: "<p><b>" + data.Message + "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
          }
        }
      },
      error: function () {},
      complete: function () {},
    });
  }
  if ($("#PassportNumber").val() != null) {
    checkNll = true;
    $.ajax({
      type: "GET",
      async: false,
      url: "/Appointment/Application/GetRejectionRefundDoneChechByPassportNumberAndNationalty",
      data: {
        nationalityId: $("#NationalityId").val(),
        passportNumber: $("#PassportNumber").val(),
      },
      success: function (data) {
        if (data) {
          if (data.Result === true) {
            bootbox.dialog({
              title: jsResources.Information,
              message: "<p><b>" + data.Message + "</b></p>",
              size: "extra-large",
              onEscape: true,
              backdrop: true,
            });
          }
        }
      },
      error: function () {},
      complete: function () {},
    });
  }
}

function emailFormatCheck() {
  let checkValue = false;
  if ($("#Email").val() != null) {
    $.ajax({
      type: "GET",
      async: false,
      url:
        "/Appointment/Application/GetEmailFormatCheckTrue?email=" +
        $("#Email").val(),
      success: function (data) {
        checkValue = data;
      },
    });
  }
  return checkValue;
}

$("#ApplicationTypeId").on("change", function () {
  checkVisaCategoryRequirement();
});

function checkVisaCategoryRequirement() {
  var applicationTypeId = $("#ApplicationTypeId").val();

  if (applicationTypeId === "11") {
    $(".additionalServiceType").show();
    $("#AdditionalServiceTypeId").attr("required", true);
    $("#AdditionalServiceTypeId").attr(
      "validationMessage",
      jsResources.RequiredField
    );
    $("#VisaCategoryId").setRequired(false);
  } else {
    $(".additionalServiceType").hide();
    $(".tsFormGroup").hide();
    $("#VisaCategoryId").attr("required", true);
    $("#VisaCategoryId").attr("validationMessage", jsResources.RequiredField);
    $("#AdditionalServiceTypeId").attr("required", false);
    $("#AdditionalServiceTypeId").data("kendoDropDownList").select(null);
    $("#VehicleTypeId").attr("required", false);
    $("#VehicleTypeId").data("kendoDropDownList").select(null);
    $("#PlateNo").attr("required", false);
    $("#ModelYear").attr("required", false);
    $("#ChassisNumber").attr("required", false);
  }
}

$("#AdditionalServiceTypeId").on("change", function () {
  setAlertExtraFee();
});

function setAlertExtraFee() {
  var additionalServiceTypeId = $("#AdditionalServiceTypeId").val();

  if (additionalServiceTypeId === "4") {
    showNotification("warning", jsResources.PleaseCheckTheExtraFees);
  }
}

$("#BirthDate").on("change", function () {
  checkAdditionalServiceTypeExtraFee();
  isVisaExemptOkButtonClicked = false;
});

$("#NationalityId").on("change", function () {
  isVisaExemptOkButtonClicked = false;
});

function checkAdditionalServiceTypeExtraFee() {
  var additionalServiceTypeId = $("#AdditionalServiceTypeId").val();
  var applicationTypeId = $("#ApplicationTypeId").val();
  var ageRange = calculateAgeRange();
  if (applicationTypeId == 11) {
    $(extraFees).each(function (i) {
      var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
      var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
      var selectedAgeRange = $("#ExtraFees_" + index + "__AgeRange").val();

      $(extraFees[i]).prop("disabled", false);
      if (
        applicationTypeId == 11 &&
        categoryTypeId != 7 &&
        additionalServiceTypeId == 4
      ) {
        $(extraFees[i]).prop("disabled", true);
        $(extraFees[i]).prop("checked", false);
      }
      if (
        (applicationTypeId == 11 &&
          categoryTypeId == 7 &&
          additionalServiceTypeId == 4 &&
          selectedAgeRange != ageRange) ||
        (applicationTypeId != 11 &&
          categoryTypeId == 7 &&
          additionalServiceTypeId != 4) ||
        (applicationTypeId == 11 &&
          categoryTypeId == 7 &&
          additionalServiceTypeId != 4)
      ) {
        var name = $(".yss_" + index);
        $(name).hide();
        $(extraFees[i]).prop("checked", false);
      }
      if (
        applicationTypeId == 11 &&
        categoryTypeId == 7 &&
        additionalServiceTypeId == 4 &&
        selectedAgeRange == ageRange
      ) {
        var name = $(".yss_" + index);
        $(name).show();
        $(extraFees[i]).prop("disabled", false);
      }
    });
  }
}

function calculateAgeRange() {
  var birthDate = kendo.parseDate(
    $("#BirthDate").val(),
    jsResources.DatePickerFormatJs
  );
  var diff_ms = Date.now() - birthDate.getTime();
  var age_dt = new Date(diff_ms);
  var age = Math.abs(age_dt.getUTCFullYear() - 1970);

  if (age <= 15) {
    return "1";
  } else if (age >= 16 && age <= 25) {
    return "2";
  } else if (age >= 26 && age <= 35) {
    return "3";
  } else if (age >= 36 && age <= 45) {
    return "4";
  } else if (age >= 46 && age <= 50) {
    return "5";
  } else if (age >= 51 && age <= 55) {
    return "6";
  } else if (age >= 56 && age <= 60) {
    return "7";
  } else if (age >= 61 && age <= 65) {
    return "8";
  } else if (age >= 66 && age < 69) {
    return "9";
  } else {
    return "0";
  }
}

$("#VehicleTypeId").on("change", function () {
  checkVehicleTypeExtraFee();
  checkAdditionalServiceTypeExtraFee();
});

function checkVehicleTypeExtraFee() {
  var additionalServiceTypeId = $("#AdditionalServiceTypeId").val();
  var applicationTypeId = $("#ApplicationTypeId").val();
  var vehicleTypeId = $("#VehicleTypeId").val();

  $(extraFees).each(function (i) {
    var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
    var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
    var extraFeeName = $("#ExtraFees_" + index + "__ExtraFeeName").val();
    if (applicationTypeId == 11) {
      if (additionalServiceTypeId != 4) {
        $(extraFees[i]).prop("disabled", false);
        if (applicationTypeId != 11 && categoryTypeId == 8) {
          var name = $(".ts_" + index);
          $(name).hide();
          $(extraFees[i]).prop("checked", false);
        } else if (applicationTypeId == 11) {
          if (
            categoryTypeId != 8 &&
            additionalServiceTypeId == 5 &&
            !(
              categoryTypeId == 1 &&
              (extraFeeName.includes("Vekalet") ||
                extraFeeName.includes("Bedel") ||
                extraFeeName.includes("Notarized"))
            )
          ) {
            $(extraFees[i]).prop("disabled", true);
            $(extraFees[i]).prop("checked", false);
          } else if (
            categoryTypeId == 8 &&
            (additionalServiceTypeId == 4 || additionalServiceTypeId == 5)
          ) {
            if (
              vehicleTypeId == 1
                ? extraFeeName.includes("Hususi") ||
                  extraFeeName.includes("Special")
                : extraFeeName.includes("Kamyonet") ||
                  extraFeeName.includes("Van")
            ) {
              var name = $(".ts_" + index);
              $(name).show();
              $(extraFees[i]).prop("disabled", false);
            } else {
              var name = $(".ts_" + index);
              $(name).hide();
              $(extraFees[i]).prop("checked", false);
            }
          } else {
            var name = $(".ts_" + index);
            $(name).show();
            $(extraFees[i]).prop("disabled", false);
          }
        }
      } else if (categoryTypeId == 8) {
        var name = $(".ts_" + index);
        $(name).hide();
        $(extraFees[i]).prop("checked", false);
      }
    } else if (categoryTypeId == 8) {
      var name = $(".ts_" + index);
      $(name).hide();
      $(extraFees[i]).prop("checked", false);
    }
  });
}

function checkVisaRejectionRefundForNewApplication() {
  $.ajax({
    type: "GET",
    async: false,
    url: "/Appointment/Application/VisaRejectionRefundForNewApplication",
    data: {
      name: $("#Name").val(),
      surname: $("#Surname").val(),
      birthDate: $("#BirthDate").val(),
      genderId: $("#GenderId").val(),
      fatherName: $("#FatherName").val(),
      motherName: $("#MotherName").val(),
    },
    success: function (data) {
      if (data.Data.Result === true) {
        bootbox.dialog({
          message: "<p><b>" + data.Message + "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
      }
    },
    error: function () {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
}

function createInsuranceWithPrint(encryptedApplicationId) {
  $.ajax({
    type: "GET",
    url:
      "/Appointment/Insurance/CreateInsuranceWithPrint?encryptedApplicationId=" +
      encryptedApplicationId,
    async: false,
    success: function (data) {
      showNotification(data.Type, data.Message);
    },
    error: function () {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });

  return true;
}

function print(PrinterApplicationId, PrinterRelationalApplicationId, isAll) {
  let applicationId = PrinterApplicationId;
  if (PrinterRelationalApplicationId != "") {
    applicationId = PrinterRelationalApplicationId;
  }

  $.ajax({
    type: "GET",
    url:
      "/Appointment/PDF/GetICR?encryptedApplicationId=" +
      getItemTextValue(applicationId, "ToEncrypt") +
      "&taxInclude=false" +
      "&icrLanguage=2" +
      "&includeAll=false" +
      "&printAll=false" +
      "&printer=true",
    async: false,
    success: function (data) {
      if (data.ResultType == 2) {
        showNotification("warning", data.Message);
      } else {
        var htmlStr_ICR = data;
        $.ajax({
          type: "POST",
          url: "http://localhost:51234/api/Print/PrintPage",
          data: JSON.stringify({
            pdfPageSize: 8,
            htmlContent: htmlStr_ICR,
            printerTypeId: 1,
            printerWidth: 850,
            printerHeight: 1131,
            isDuplex: true,
            webPageWidth: 800,
            webPageHeight: 1000,
            MarginTop: 20,
            bottomBottom: 20,
          }),
          async: false,
          success: function (data) {
            if (data.message != null) {
              showNotification("warning", data.message);
            }
          },
          error: function () {
            showNotification("danger", jsResources.ErrorOccurred);
          },
          dataType: "json",
          contentType: "application/json",
        });
      }
    },
    error: function () {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });

  if (isAll === "true" || isAll === "True") {
    $.ajax({
      type: "GET",
      url:
        "/Appointment/PDF/ApplicationPage_?encryptedApplicationId=" +
        getItemTextValue(applicationId, "ToEncrypt") +
        "&relationalId=" +
        PrinterRelationalApplicationId,
      async: false,
      success: function (data) {
        if (data.ResultType == 2) {
          showNotification("warning", data.Message);
        } else {
          for (const app of data) {
            $.ajax({
              type: "POST",
              url: "http://localhost:51234/api/Print/PrintPage",
              data: JSON.stringify({
                pdfPageSize: 8,
                htmlContent: app,
                printerTypeId: 5,
                printerWidth: 850,
                isDuplex: true,
                printerHeight: 1131,
                webPageWidth: 800,
                webPageHeight: 1000,
                MarginTop: 20,
                bottomBottom: 20,
              }),
              async: false,
              success: function (data) {
                if (data.message != null) {
                  showNotification("warning", data.message);
                }
              },
              error: function () {
                showNotification("danger", jsResources.ErrorOccurred);
              },
              dataType: "json",
              contentType: "application/json",
            });
          }
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
      },
      dataType: "json",
    });
  } else {
    $.ajax({
      type: "GET",
      url:
        "/Appointment/PDF/ApplicationPage?encryptedApplicationId=" +
        getItemTextValue(applicationId, "ToEncrypt") +
        "&relationalId=" +
        PrinterRelationalApplicationId,
      async: false,
      success: function (data) {
        if (data.ResultType == 2) {
          showNotification("warning", data.Message);
        } else {
          var applicationPageHtml = data;
          $.ajax({
            type: "POST",
            url: "http://localhost:51234/api/Print/PrintPage",
            data: JSON.stringify({
              pdfPageSize: 8,
              htmlContent: applicationPageHtml,
              printerTypeId: 5,
              printerWidth: 850,
              isDuplex: true,
              printerHeight: 1131,
              webPageWidth: 800,
              webPageHeight: 1000,
              MarginTop: 20,
              bottomBottom: 20,
            }),
            async: false,
            success: function (data) {
              if (data.message != null) {
                showNotification("warning", data.message);
              }
            },
            error: function () {
              showNotification("danger", jsResources.ErrorOccurred);
            },
            dataType: "json",
            contentType: "application/json",
          });
        }
      },
      error: function () {
        showNotification("danger", jsResources.ErrorOccurred);
      },
    });
  }

  $.ajax({
    type: "GET",
    url:
      "/Appointment/PDF/GetBarcode_?encryptedId=" +
      getItemTextValue(applicationId, "ToEncrypt") +
      "&direction=GetPrinterBarcode",
    async: false,
    success: function (data) {
      var htmlStr_Bracode = data;
      $.ajax({
        type: "POST",
        url: "http://localhost:51234/api/Print/PrintPage",
        data: JSON.stringify({
          pdfPageSize: 0,
          htmlContent: htmlStr_Bracode,
          printerTypeId: 3,
          docWidth: 400,
          docHeight: 190,
          printerWidth: 400,
          isDuplex: false,
          printerHeight: 195,
          webPageWidth: 500,
          webPageHeight: 150,
          MarginTop: 0,
          bottomBottom: 0,
        }),
        async: false,
        success: function (data) {
          if (data.message != null) {
            showNotification("warning", data.message);
          }
        },
        error: function () {
          showNotification("danger", jsResources.ErrorOccurred);
        },
        dataType: "json",
        contentType: "application/json",
      });
    },
    error: function () {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
}

function checkApplicantCount() {
  var result = true;
  $.ajax({
    type: "GET",
    url:
      "/Appointment/Application/IsLastApplication?ApplicationId=" +
      getItemTextValue($("#EncryptedApplicationId").val(), "ToDecryptInt"),
    async: false,
    success: function (data) {
      if (data.Type === "success") {
        if (
          data.Data.Result === true &&
          $("#ApplicantCount").val() < data.Data.ApplicantCount
        ) {
          result = false;
        }
      }
    },
    error: function () {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
  return result;
}

function IsNotCreatedInsuranceExist(PrinterApplicationId) {
  var result = false;
  $.ajax({
    type: "GET",
    url:
      "/Appointment/Application/IsNotCreatedInsuranceExist?applicationId=" +
      PrinterApplicationId,
    async: false,
    success: function (data) {
      if (data.Type === "success") {
        if (data.Data.Result === true) {
          result = true;
        }
      }
    },
    error: function () {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
  return result;
}

function SapCreateOrder(EncryptedRelationalApplicationId) {
  $.ajax({
    type: "GET",
    url: "/Appointment/Application/CreateOrder",
    data: { encryptedApplicationId: EncryptedRelationalApplicationId },
    async: false,
    success: function (data) {
      showNotification(data.Type, data.Message);
    },
    error: function (data) {
      showNotification("danger", jsResources.ErrorOccurred);
    },
  });
}

//

$("#Email").on("change", changeContactInformation);
$("#PhoneNumber1").on("change", changeContactInformation);

function changeContactInformation() {
  var disable = $("#DisableContactInformationVerify").val();
  if (!(disable === "True" || disable === "true")) {
    $("#contactInformationVerifiy").show();
    $("#IsContactInformationVerified").val("False");

    $("#IsContactInformationVerified").prop("checked", false);
    $("#IsContactInformationVerified").attr("required", true);
  } else {
    $("#IsContactInformationVerified").attr("required", false);
    $("#IsContactInformationVerified").prop("checked", true);
  }
}

$("#contactInformationVerifiy").on("click", function () {
  $(".modal-content").css("background-color", "#FFFFFF");
  $("#divVeriyfByUser").hide();
  $("#mail").html("(" + $("#Email").val() + ")");
  $("#tel").html("(" + $("#PhoneNumber1").val() + ")");
  $("#veriyfContactInformationVerifyCode").prop("disabled", true);
  $("#veriyfContactInformationCreateCode").prop("disabled", false);
  $("#code").val("");
  $("#veriyfTime").html("");
  var typ = $("input[name='verificationMethod']:checked").val();

  if (typ == 3) {
    $("#divVeriyfByUser").show();
    $("#divVeriyfByCode").hide();
    $("#veriyfContactInformationCreateCode").hide();
  } else {
    $("#divVeriyfByUser").hide();
    $("#divVeriyfByCode").show();
    $("#veriyfContactInformationCreateCode").show();
  }
});
$("input[name='verificationMethod']").on("click", function () {
  var typ = $("input[name='verificationMethod']:checked").val();

  if (typ == 3) {
    $("#divVeriyfByUser").show();
    $("#divVeriyfByCode").hide();
    $("#veriyfContactInformationCreateCode").hide();
  } else {
    $("#divVeriyfByUser").hide();
    $("#divVeriyfByCode").show();
    $("#veriyfContactInformationCreateCode").show();
  }
});

var interval = null;
$("#veriyfContactInformationCreateCode").on("click", function () {
  event.preventDefault();
  var typ = $("input[name='verificationMethod']:checked").val();
  var info =
    typ == "1"
      ? $("#CountryCallingCode").val() + $("#PhoneNumber1").val()
      : $("#Email").val();

  $("#veriyfContactInformationCreateCode").prop("disabled", true);
  $.ajax({
    type: "POST",
    url: "/Appointment/Application/ContactInformationGenerateVerificationCode",
    data: JSON.stringify({
      method: typ,
      contactInformation: info,
      passportNumber: $("input[name=PassportNumber]").val(),
    }),
    async: false,
    success: function (data) {
      if (data.IsSuccess) {
        localStorage.setItem("hash", data.Data.Hash);
        localStorage.setItem("expirationTime", data.Data.ExpirationTime);
        var time = 60;

        $("#veriyfContactInformationVerifyCode").prop("disabled", false);
        interval = setInterval(() => {
          time--;
          $("#veriyfTime").html("(" + time + ")");
          if (time < 1) {
            $("#veriyfContactInformationCreateCode").prop("disabled", false);
            $("#veriyfContactInformationVerifyCode").prop("disabled", true);
            clearInterval(interval);
            $("#veriyfTime").html("");
          }
        }, 1000);
      } else {
        $("#veriyfContactInformationCreateCode").prop("disabled", false);
        $("#veriyfContactInformationVerifyCode").prop("disabled", true);
        bootbox.dialog({
          message: '<p style="color:#FF0000;"><b>' + data.Message + "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
      }
    },
    error: function (data) {
      $("#veriyfContactInformationCreateCode").prop("disabled", false);
      $("#veriyfContactInformationVerifyCode").prop("disabled", true);
      bootbox.dialog({
        message: '<p style="color:#FF0000;"><b>' + data.Message + "</b></p>",
        size: "extra-large",
        onEscape: true,
        backdrop: true,
      });
    },
    dataType: "json",
    contentType: "application/json",
  });
});
$("#veriyfContactInformationVerifyCode").on("click", function (event) {
  event.preventDefault();
  $.ajax({
    type: "POST",
    url: "/Appointment/Application/ContactInformationVerifyVerificationCode",
    data: JSON.stringify({
      code: $("#code").val(),
      hash: localStorage.getItem("hash"),
      expirationTime: localStorage.getItem("expirationTime"),
    }),
    async: true,
    success: function (data) {
      if (data.IsSuccess) {
        $("#exampleModal1").modal("hide");
        $("#contactInformationVerifiy").hide();
        $("#IsContactInformationVerified_validationMessage").hide();
        $("#IsContactInformationVerified").attr("required", false);
        $("#IsContactInformationVerified").val("True");

        $("#IsContactInformationVerified").prop("checked", true);
        clearInterval(interval);
      } else {
        bootbox.dialog({
          message: '<p style="color:#FF0000;"><b>' + data.Message + "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
      }
    },
    error: function (data) {
      $("#exampleModal1").modal("hide");
      bootbox.dialog({
        message: '<p style="color:#FF0000;"><b>' + data.Message + "</b></p>",
        size: "extra-large",
        onEscape: true,
        backdrop: true,
      });
    },
    dataType: "json",
    contentType: "application/json",
  });
});

$("#veriyfContactInformationByUser").on("click", function () {
  event.preventDefault();
  $.ajax({
    type: "POST",
    url: "/Appointment/Application/ContactInformationVerifyUser",
    data: JSON.stringify({
      username: $("#Username").val(),
      password: $("#Password").val(),
      passportNumber: $("input[name=PassportNumber]").val(),
    }),
    async: false,
    success: function (data) {
      if (data.IsSuccess) {
        $("#exampleModal1").modal("hide");
        $("#contactInformationVerifiy").hide();
        $("#IsContactInformationVerified_validationMessage").hide();
        $("#IsContactInformationVerified").attr("required", false);
        $("#IsContactInformationVerified").val("True");
        $("#IsContactInformationVerified").prop("checked", true);
      } else {
        bootbox.dialog({
          message: '<p style="color:#FF0000;"><b>' + data.Message + "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
      }
    },
    error: function (data) {
      $("#exampleModal1").modal("hide");
      bootbox.dialog({
        message: '<p style="color:#FF0000;"><b>' + data.Message + "</b></p>",
        size: "extra-large",
        onEscape: true,
        backdrop: true,
      });
    },
    dataType: "json",
    contentType: "application/json",
  });
});

function checkVisaExemption(wizard) {
  var birthDate = $("#BirthDate").val();
  var nationalityId = $("#NationalityId").val();

  return $.ajax({
    url: "/Appointment/Application/CheckVisaExemption",
    type: "POST",
    data: {
      birthDate: birthDate,
      nationalityId: nationalityId,
    },
    success: function (response) {
      var message = response.Message;
      if (message) {
        $("#VisaExemption-Window")
          .data("kendoWindow")
          .content(
            "<p>" +
              message +
              '</p><br/><button id="okButton" class="k-button">OK</button>'
          );
        $("#VisaExemption-Window").data("kendoWindow").open();
        $("#VisaExemption-Window").data("kendoWindow").center();

        $("#okButton").click(function () {
          $("#VisaExemption-Window").data("kendoWindow").close();
          isVisaExemptOkButtonClicked = true;
        });
      } else {
        goToNewStep(wizard);
      }
    },
    error: function () {
      console.error("Error checking visa exemption.");
    },
  });
}

function checkPaymentMethods() {
  var result = true;
  $(extraFees).each(function (i) {
    var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
    if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
      if (
        !(
          $("#ExtraFees_" + index + "__Cash").is(":checked") ||
          $("#ExtraFees_" + index + "__Pos").is(":checked") ||
          $("#ExtraFees_" + index + "__Online").is(":checked")
        )
      ) {
        bootbox.dialog({
          message:
            '<p style="color:#FFFFFF;"><b>' +
            jsResources.PaymentMethodsWarning +
            "</b></p>",
          size: "extra-large",
          onEscape: true,
          backdrop: true,
        });
        $(".modal-content").css("background-color", "#FFA800");
        result = false;
        return false;
      }
    }
  });
  return result;
}
$(document).ready(function () {
  var original = $("#VisaExemption-Window").clone(true);

  $(".box-col input").change(function () {
    var clone = original.clone(true);
    $("#VisaExemption-Window").data("kendoWindow").destroy();

    setTimeout(function () {
      $("#ApplicationVisaExemption").append(clone);
      initWindow();
    }, 200);
  });

  var getEffects = function () {
    return "expand:vertical " + "fadeIn";
  };

  function initWindow() {
    var windowOptions = {
      actions: [],
      draggable: true,
      resizable: true,
      width: 500,
      title: jsResources.VisaExemption,
      visible: false,
    };

    windowOptions.animation = {
      open: { effects: getEffects() },
      close: { effects: getEffects(), reverse: true },
    };

    $("#VisaExemption-Window").kendoWindow(windowOptions);
  }

  initWindow();
});
