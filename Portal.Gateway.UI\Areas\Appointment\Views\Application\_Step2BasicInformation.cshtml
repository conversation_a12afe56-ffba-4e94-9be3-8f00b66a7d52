@model AddUpdateApplicationViewModel

<div id="divStep2BasicInformation" class="pb-5" data-wizard-type="step-content">
	@Html.HiddenFor(m => m.AreaName)
	@Html.HiddenFor(m => m.GovernorateName)
	@Html.HiddenFor(m => m.IsPhoneNumberRejectionCheckActive)
	<h4 class="mb-10 font-weight-bold text-dark">@SiteResources.ApplicationStep2BasicInformation.ToTitleCase()</h4>
	<div class="row">
		<div class="col-xl-12">
			<div class="form-group row">
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.Title.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.TitleId)
												.HtmlAttributes(new { @class = "form-control" })
												.Events(events => events.Change("onChangeTitle"))
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetTitleSelectList", "Parameter", new { Area = "" });
													});
												}))
					<span asp-validation-for="TitleId"></span>
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
					<input type="text" asp-for="Name" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
					<span asp-validation-for="Name"></span>
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.Surname.ToTitleCase()</label>
					<input type="text" asp-for="Surname" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
					<span asp-validation-for="Surname"></span>
				</div>
			</div>
			<div class="form-group row">
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.BirthDate.ToTitleCase()</label>
					@(Html.Kendo().DatePickerFor(m => m.BirthDate)
												.Format(SiteResources.DatePickerFormatView)
												.Max(DateTime.Now)
												.Value((Model.IsExistingApplication || Model.IsFromPreApplication) ? Model.BirthDate : DateTime.Today))
					<span asp-validation-for="BirthDate"></span>
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.Gender.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.GenderId)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetGenderSelectList", "Parameter", new { Area = "" });
													});
												}))
					<span asp-validation-for="GenderId"></span>
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.MaritalStatus.ToTitleCase()</label>
					@(Html.Kendo().DropDownListFor(m => m.MaritalStatusId)
												.HtmlAttributes(new { @class = "form-control" })
												.Filter(FilterType.Contains)
												.OptionLabel(SiteResources.Select)
												.DataTextField("Text")
												.DataValueField("Value")
												.DataSource(source =>
												{
													source.Read(read =>
													{
														read.Action("GetMaritalStatusSelectList", "Parameter", new { Area = "" });
													});
												}))
					<span asp-validation-for="MaritalStatusId"></span>
				</div>
			</div>
			<div class="form-group row">
				<div class="applicationMaidenName col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.MaidenName.ToTitleCase()</label>
					<input type="text" asp-for="MaidenName" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.FatherName.ToTitleCase()</label>
					<input type="text" asp-for="FatherName" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
					<span asp-validation-for="FatherName"></span>
				</div>
				<div class="col-lg-3 col-md-6">
					<label class="font-weight-bold">@SiteResources.MotherName.ToTitleCase()</label>
					<input type="text" asp-for="MotherName" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
					<span asp-validation-for="MotherName"></span>
				</div>
			</div>
			<div class="form-group row">
                <div class="col-lg-3 col-md-6" id="divEmail">
                    <label class="font-weight-bold">@SiteResources.Email.ToTitleCase()</label>
                    <input type="email" asp-for="Email" class="form-control" autocomplete="off" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="Email"></span>
                </div>
                <div class="col-lg-3 col-md-6 pb-3 d-flex align-items-center" id="divHasNoEmail">
                    <div class="k-checkbox-wrap" style="margin-top: auto; margin-bottom: 0;">
                        @(Html.Kendo().CheckBoxFor(m => m.HasNoEmail).HtmlAttributes(new { @class = "checkbox-square", onchange = "onHasNoEmailChanged();" }).Label(SiteResources.NoEmailLabelName.ToTitleCase()))
                    </div>
                </div>
				<div class="col-lg-3 col-md-6" id="divPhone1">
					<label class="font-weight-bold">@SiteResources.PhoneNumber.ToTitleCase() #1</label>
					<div class="input-group">
						<div class="input-group-prepend">
							<span class="input-group-text">+@Model.CountryCallingCode</span>
						</div>
						<input type="text" asp-for="PhoneNumber1" class="form-control" autocomplete="off" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" />
					</div>
					<span asp-validation-for="PhoneNumber1"></span>
				</div>
				<div class="col-lg-3 col-md-6" id="divPhone2">
					<label class="font-weight-bold">@SiteResources.PhoneNumber.ToTitleCase() #2</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+@Model.CountryCallingCode</span>
                        </div>
                        <input type="text" asp-for="PhoneNumber2" class="form-control" autocomplete="off" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" />
                    </div>
					<span asp-validation-for="PhoneNumber2"></span>
				</div>
                <div id="contactInformationVerification" class="col-lg-3 col-md-6">
                    <button id="contactInformationVerifiy" type="button" style="margin-top:24px;" class="btn btn-primary font-weight-bold" data-toggle="modal" data-target="#verificationModal1">@SiteResources.Verify</button>
                    <input type="checkbox" style="display:none" asp-for="IsContactInformationVerified" />
                    <span asp-validation-for="IsContactInformationVerified"></span>
                </div>
				<input type="hidden" asp-for="DisableContactInformationVerify" />
			</div>
			<div class="form-group row">
				<div class="col-lg-12">
					<label class="font-weight-bold">@SiteResources.Address.ToTitleCase()</label>
					<input type="text" asp-for="Address" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
					<span asp-validation-for="Address"></span>
				</div>
			</div>
			<div class="form-group row">
				@if (Model.ShowCityDropdown)
				{
					<div class="col-lg-3 col-md-6">
						<label class="font-weight-bold">@SiteResources.City.ToTitleCase()</label>
						@(Html.Kendo().DropDownListFor(m => m.ForeignCityId)
													.HtmlAttributes(new { @class = "form-control" })
													.Events(events => events.Change("onForeignCitySelected"))
													.Filter(FilterType.Contains)
													.OptionLabel(SiteResources.Select)
													.DataTextField("Text")
													.DataValueField("Value")
													.DataSource(source =>
													{
														source.Read(read =>
														{
															read.Action("GetForeignCitySelectList", "Parameter", new { Area = "" });
														});
													}))
						<span asp-validation-for="ForeignCityId"></span>
					</div>
				}
                @if (Model.IsCargoIntegrationActive && Model.CargoProviderId is (int)CargoProviderType.UPS)
                {
                    <div class="col-lg-3 col-md-6">
                        <label class="font-weight-bold">@SiteResources.PostalCode.ToTitleCase()</label>
                        <input type="text" asp-for="PostalCode" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label class="font-weight-bold">@SiteResources.NameOfSecondContactPerson.ToTitleCase()</label>
                        <input type="text" asp-for="NameOfSecondContactPerson" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    </div>
                }
			</div>
			<div class="form-group row">
				@if (Model.IsCargoIntegrationActive && Model.CargoProviderId is (int)CargoProviderType.LastMile)
				{
					<div class="col-lg-3 col-md-6">
						@(Html.Kendo().CheckBoxFor(m => m.IsCargoIntegrationSelected)
													.HtmlAttributes(new { @class = "form-control", onchange = "onCargoIntegrationClicked();" })
													.Label(SiteResources.UseCargoIntegration.ToTitleCase()))
					</div>
					<div class="col-lg-3 col-md-6" style="display: none" id="GovernorateDiv">
						<label class="font-weight-bold">@SiteResources.GovernorateInformation.ToTitleCase()</label>
						@(Html.Kendo().DropDownListFor(m => m.GovernorateId)
													.HtmlAttributes(new { @class = "form-control" })
													.Filter(FilterType.Contains)
													.OptionLabel(SiteResources.Select)
													.Events(events => events.Change("OnGovernorateSelected"))
													.DataTextField("Text")
													.DataValueField("Value")
													.AutoBind(false)
													.DataSource(source =>
													{
														source.Read(read =>
														{
															read.Action("GetGovernorateSelectList", "Parameter", new { Area = "" });
														});
													}))
					</div>
					<div class="col-lg-6 col-md-6" style="display: none" id="AreaDiv">
						<label class="font-weight-bold">@SiteResources.AddressAreaInformation.ToTitleCase()</label>
						@(Html.Kendo().DropDownListFor(m => m.AreaId)
													.HtmlAttributes(new { @class = "form-control" })
													.Filter(FilterType.Contains)
													.OptionLabel(SiteResources.Select)
													.Events(events => events.Change("OnAreaSelected"))
													.DataTextField("Text")
													.DataValueField("Value")
													.AutoBind(false)
													.CascadeFrom("GovernorateId")
													.DataSource(source =>
													{
														source.Read(read =>
														{
															read.Action("GetAreaSelectList", "Parameter", new { Area = "" }).Data("filterGovernorate");
														}).ServerFiltering(true);
													}))
						<span asp-validation-for="AreaId"></span>
					</div>
				}
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="verificationModal1" tabindex="-1" role="dialog" aria-labelledby="verificationModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="verificationModalLabel">
					@SiteResources.VeriyfModalTitle.ToTitleCase()
				</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="false">&times;</span>
				</button>
			</div>
			<div class="modal-body ignore-empty">
				<div class="row">
					<div class="col-md-6" id="selectMethod">
						<br />
						<div class="form-check">
							<input class="form-check-input"
								   type="radio"
								   name="verificationMethod"
								   value="1"
								   id="viasms" />
							<label class="font-weight-bold" for="viasms"> SMS <span id="tel"></span></label>
						</div>
						<div class="form-check">
							<input class="form-check-input"
								   type="radio"
								   name="verificationMethod"
								   value="2"
								   id="viaemail" />
							<label class="font-weight-bold" for="viaemail"> E-Mail <span id="mail"></span></label>
						</div>
						<div class="form-check">
							<input class="form-check-input"
								   type="radio"
								   name="verificationMethod"
								   id="viasupervisor"
								   value="3" />
							<label class="font-weight-bold" for="viasupervisor"> Supervisor	</label>
						</div>
						<div class="text-right">
							<button id="veriyfContactInformationCreateCode" class="btn btn-primary ">@SiteResources.Send.ToTitleCase()</button>
						</div>
					</div>
					<div class="col-md-6" style="border-left:solid 1px;">
						<div id="divVeriyfByUser">
							<br />
							<div class="form-group" style="">
								<input type="text" placeholder="Username" id="Username" name="UseraName" class="form-control" autocomplete="off" />
							</div>
							<div class="form-group" style="">
								<input type="password" id="Password" placeholder="Password" name="Password" class="form-control" autocomplete="off" />
							</div>
							<div class="text-right">
								<button id="veriyfContactInformationByUser" class="btn btn-success">Veriyf</button>
							</div>
						</div>
						<div id="divVeriyfByCode">
							<div class="form-group">
								<label class="font-weight-bold">@SiteResources.Code.ToTitleCase()</label>
								<input type="text" id="code" class="form-control" autocomplete="off" />
							</div>
							<div class="text-right">
								<button id="veriyfContactInformationVerifyCode" class="btn btn-success">@SiteResources.Send.ToTitleCase() <span id="veriyfTime"></span></button>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">@SiteResources.Close.ToTitleCase()</button>
			</div>
		</div>
	</div>
</div>
<script>

	$(function () {
		checkMaidenNameRequirement();
		checkForeignCityRequirement();
		onCargoIntegrationClicked();
		checkIsContactInformationVerifiedValidation();
		checkHasNoEmail();
        const FormElements = isRequired();
		if (FormElements[0] !== 0) { //if array null returns 0
			const FormElementMaritalStatusIdId = new Array();
			for (let i = 0; i < FormElements.length; i++) {
				FormElementMaritalStatusIdId.push(FormElements[i]);
			}
			var isExist = FormElementMaritalStatusIdId.indexOf(15);
			if (isExist != -1) {
				$("#MaritalStatusId").attr("required", true);
				$("#MaritalStatusId").attr("validationMessage", jsResources.RequiredField);
			}
		}
	});

	function checkHasNoEmail(){
		var isExistingApplication = '@Model.IsExistingApplication';

		if (isExistingApplication === "false" || isExistingApplication === "False") {
			return
		}

		var hasNoEmailCheckbox = $("#HasNoEmail");
		var email = $("#Email").val();
		hasNoEmailCheckbox.prop('checked', email === "<EMAIL>");
	}

	function onForeignCitySelected() {
		if ('@Model.CargoProviderId' === '1' && ('@Model.IsCargoIntegrationActive' === "true" || '@Model.IsCargoIntegrationActive' === "True")) {
            var postalCode = getItemTextValue($("#ForeignCityId").val(), "GetForeignCityPostalCode");
            $("#PostalCode").val(postalCode);
        }
    }

	function checkForeignCityRequirement() {
		if ('@Model.ShowCityDropdown' === "true" || '@Model.ShowCityDropdown' === "True") {
			$("#ForeignCityId").attr("required", true);
			$("#ForeignCityId").attr("validationMessage", jsResources.RequiredField);
		}
	}

	function checkIsContactInformationVerifiedValidation() {
		if ('@Model.DisableContactInformationVerify' != 'True' && '@Model.IsContactInformationVerified' != 'True' && (($("#ApplicantTypeId").val() === '@ApplicantType.Family.ToInt()' && '@Model.IsMainApplicant' == 'True') || $("#ApplicantTypeId").val() != '@ApplicantType.Family.ToInt()')) {
			$("#IsContactInformationVerified").attr("required", true);
			$("#IsContactInformationVerified").attr("validationMessage", jsResources.RequiredField);
			$('#viasms').prop('checked', true);
			$("#contactInformationVerification").show();
		}
		else {
			$('#IsContactInformationVerified').prop('checked', true);
			$("#contactInformationVerification").hide();
		}
	}

    function checkMaidenNameRequirement() {
		var isExistingApplication = '@Model.IsExistingApplication';

		if ($("#TitleId").val() === '@Title.Mrs.ToInt()') {
			$(".applicationMaidenName").show();
		}
		else {
			$(".applicationMaidenName").hide();
		}

		if (isExistingApplication === "false" || isExistingApplication === "False") {
			$("#MaidenName").val('');
		}

		var isTurkmenistanApplication = IsTurkmenistanApplication($("#CountryCallingCode").val());

		if (isTurkmenistanApplication) {

			if ($("#TitleId").val() === '@Title.Mrs.ToInt()') {
				RequireMaidenName();
			}
			else {
				RemoveMaidenNameRequirements();
			}
		}
	}

	function onChangeTitle() {
		if ($("#TitleId").val() === '@Title.Mrs.ToInt()') {
			$(".applicationMaidenName").show();
		}
		else {
			$(".applicationMaidenName").hide();
		}
		$("#MaidenName").val('');

		if ($("#TitleId").val() === '@Title.Mr.ToInt()') {
			$("#GenderId").data("kendoDropDownList").select(@Gender.Male.ToInt());
		}
		else if ($("#TitleId").val() === '@Title.Mrs.ToInt()') {
			$("#GenderId").data("kendoDropDownList").select(@Gender.Female.ToInt());
		}
		else if ($("#TitleId").val() === '@Title.Ms.ToInt()') {
			$("#GenderId").data("kendoDropDownList").select(@Gender.Female.ToInt());
		}
		else {
			$("#GenderId").data("kendoDropDownList").select(null);
		}

		var isTurkmenistanApplication = IsTurkmenistanApplication($("#CountryCallingCode").val());

		if (isTurkmenistanApplication) {

			if ($("#TitleId").val() === '@Title.Mrs.ToInt()') {
				RequireMaidenName();
			}
			else {
				RemoveMaidenNameRequirements();
			}
		}
	}

	function IsTurkmenistanApplication(callingCode) {
		return callingCode === '993';
	}

	function RequireMaidenName() {
		$("#MaidenName").attr("required", true);
		$("#MaidenName").attr("validationMessage", jsResources.RequiredField);
	}

	function RemoveMaidenNameRequirements() {
		$("#MaidenName").attr("required", false);
	}

	function onCargoIntegrationClicked() {
		var areaDropDownList = $("#AreaId").data("kendoDropDownList");
		var governorateDropDownList = $("#GovernorateId").data("kendoDropDownList");

		if (areaDropDownList !== undefined && areaDropDownList !== 'undefined') {
			if ($("#IsCargoIntegrationSelected").is(":checked")) {
				$("#IsCargoIntegrationSelected").val("true");
				governorateDropDownList.dataSource.read();
				$("#GovernorateDiv").show();
				$("#AreaDiv").show();
				$("#AreaId").attr("required", true);
				$("#AreaId").attr("validationMessage", jsResources.RequiredField);
			} else {
				$("#IsCargoIntegrationSelected").val("false");
				governorateDropDownList.select(null);
				$("#GovernorateDiv").hide();
				$("#AreaDiv").hide();
				$("#AreaId").attr("required", false);
			}
		}
	}

	function OnAreaSelected() {
		const dropDownListText = $("#AreaId").data("kendoDropDownList").text();

		if (dropDownListText != undefined && dropDownListText !== jsResources.Select) {
			$("#AreaName").val(dropDownListText);
		} else {
			$("#AreaName").val(null);
		}
	}

	function OnGovernorateSelected() {
		const dropDownListText = $("#GovernorateId").data("kendoDropDownList").text();

		if (dropDownListText != undefined && dropDownListText !== jsResources.Select) {
			$("#GovernorateName").val(dropDownListText);
		} else {
			$("#GovernorateName").val(null);
		}
	}

	function filterGovernorate() {
		var filter = $('#AreaId').data('kendoDropDownList').dataSource.filter();
		var text = "";

		if (filter && filter.filters[0].operator == "contains") {
			text = filter.filters[0].value;
		}

		return {
			governorateId: $("#GovernorateId").val(),
			text: text
		}
	}

	function onHasNoEmailChanged() {
		var hasNoEmailCheckbox = $("#HasNoEmail");
		var emailInput = $("#Email");

		var isChecked = hasNoEmailCheckbox.is(':checked');

		if (isChecked) {
			emailInput.val("<EMAIL>");
		} else {
			emailInput.val("");
		}
	}

</script>