﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActivatedSuccessfully" xml:space="preserve">
    <value>Successfully activated</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="ActiveTooltip" xml:space="preserve">
    <value>The authorities of companies that do not have an active selection will be disabled</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>Add branch</value>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>Add company</value>
  </data>
  <data name="AddedSuccessfully" xml:space="preserve">
    <value>Successfully added</value>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>Add role</value>
  </data>
  <data name="AddExtraFee" xml:space="preserve">
    <value>Add extra fee</value>
  </data>
  <data name="AdminLogin" xml:space="preserve">
    <value>Admin login</value>
  </data>
  <data name="AdminNotFound" xml:space="preserve">
    <value>Admin not found</value>
  </data>
  <data name="AreYouSureToDeleteThisRecord" xml:space="preserve">
    <value>Are you sure to delete this record?</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="BranchAddress" xml:space="preserve">
    <value>Branch address</value>
  </data>
  <data name="BranchCityName" xml:space="preserve">
    <value>Branch city name</value>
  </data>
  <data name="BranchCountryName" xml:space="preserve">
    <value>Branch country name</value>
  </data>
  <data name="BranchDetails" xml:space="preserve">
    <value>Branch details</value>
  </data>
  <data name="BranchEmail" xml:space="preserve">
    <value>Branch email</value>
  </data>
  <data name="BranchList" xml:space="preserve">
    <value>Branch list</value>
  </data>
  <data name="BranchMission" xml:space="preserve">
    <value>Branch mission</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>Branch name</value>
  </data>
  <data name="BranchTelephone" xml:space="preserve">
    <value>Branch telephone</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Company details</value>
  </data>
  <data name="CompanyList" xml:space="preserve">
    <value>Company list</value>
  </data>
  <data name="CompanyModuleMatchExp" xml:space="preserve">
    <value>The number of licenses and license validity periods can be defined in the fields opened by selecting the checkboxes next to the modules to be activated and modules can be added</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Company name</value>
  </data>
  <data name="CorporateId" xml:space="preserve">
    <value>Corporate id</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="DatePickerFormatJs" xml:space="preserve">
    <value>dd/MM/yyyy</value>
  </data>
  <data name="DatePickerFormatView" xml:space="preserve">
    <value>dd/MM/yyyy</value>
  </data>
  <data name="TimePickerFormatView" xml:space="preserve">
    <value>HH:mm</value>
  </data>
  <data name="DbConnection" xml:space="preserve">
    <value>Database connection</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DeletedSuccessfully" xml:space="preserve">
    <value>Successfully deleted</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due date</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EnterYourDetailsToLogin" xml:space="preserve">
    <value>Enter your details to login to your account</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Error occurred</value>
  </data>
  <data name="ExtraFeeDetails" xml:space="preserve">
    <value>Extra fee details</value>
  </data>
  <data name="ExtraFeeName" xml:space="preserve">
    <value>Extra fee name</value>
  </data>
  <data name="ExtraFeeType" xml:space="preserve">
    <value>Extra fee type</value>
  </data>
  <data name="ExtraFeeTypes" xml:space="preserve">
    <value>Extra fee types</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="IndicatesRequiredFields" xml:space="preserve">
    <value>Indicates required fields</value>
  </data>
  <data name="Languages" xml:space="preserve">
    <value>Languages</value>
  </data>
  <data name="LicenseCount" xml:space="preserve">
    <value>License count</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="ModuleManagement" xml:space="preserve">
    <value>Module management</value>
  </data>
  <data name="ModuleOperations" xml:space="preserve">
    <value>Module operations</value>
  </data>
  <data name="MultiCase" xml:space="preserve">
    <value>Multi case</value>
  </data>
  <data name="MustBeGreateThanZero" xml:space="preserve">
    <value>Must be greater than zero</value>
  </data>
  <data name="OperationIsSuccessful" xml:space="preserve">
    <value>Operation is successful</value>
  </data>
  <data name="Operations" xml:space="preserve">
    <value>Operations</value>
  </data>
  <data name="PageNotFound" xml:space="preserve">
    <value>Page not found</value>
  </data>
  <data name="PassivatedSuccessfully" xml:space="preserve">
    <value>Successfully passivated</value>
  </data>
  <data name="Passive" xml:space="preserve">
    <value>Passive</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="RequiredField" xml:space="preserve">
    <value>Required field</value>
  </data>
  <data name="RoleDescription" xml:space="preserve">
    <value>Role description</value>
  </data>
  <data name="RoleDetails" xml:space="preserve">
    <value>Rol details</value>
  </data>
  <data name="RoleList" xml:space="preserve">
    <value>Role list</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Role name</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="SelectToAddModule" xml:space="preserve">
    <value>Module can be added if checked</value>
  </data>
  <data name="ServerError" xml:space="preserve">
    <value>Server error</value>
  </data>
  <data name="Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="SomethingWentWrong" xml:space="preserve">
    <value>Something went wrong</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="UpdateBranch" xml:space="preserve">
    <value>Update branch</value>
  </data>
  <data name="UpdateCompany" xml:space="preserve">
    <value>Update company</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_1" xml:space="preserve">
    <value>Information can be entered by selecting the checkbox to define a new module. New module definition will be completed with the add button</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_2" xml:space="preserve">
    <value>There are update and delete buttons next to the passive modules. After a module is not deleted completely, it is always stored as a passive operation in the database. Passive modules can be updated and activated by selecting the checkbox</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_3" xml:space="preserve">
    <value>An active module can be reversed by deselecting the checkbox and updated, so that the authorization of the relevant users is suspended</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_4" xml:space="preserve">
    <value>With the delete buttons on active / passive modules, module registration and connections can be completely deleted</value>
  </data>
  <data name="UpdatedSuccessfully" xml:space="preserve">
    <value>Successfully updated</value>
  </data>
  <data name="UpdateRole" xml:space="preserve">
    <value>Update role</value>
  </data>
  <data name="UpdateExtrafee" xml:space="preserve">
    <value>Update extra fee</value>
  </data>
  <data name="UserLogin" xml:space="preserve">
    <value>User login</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="UserProfile" xml:space="preserve">
    <value>User profile</value>
  </data>
  <data name="ActiveModuleCount" xml:space="preserve">
    <value>Active module count</value>
  </data>
  <data name="AddNewModule" xml:space="preserve">
    <value>Add new module</value>
  </data>
  <data name="UpdateExistingModules" xml:space="preserve">
    <value>Update existing modules</value>
  </data>
  <data name="AddCompanyModuleExp" xml:space="preserve">
    <value>New modules that have not been defined before can be added to the company via this form. It is necessary to continue with 'Update Existing Modules' in order to operate on previously defined modules whose status is active or passive.</value>
  </data>
  <data name="UpdateCompanyModeuleExp" xml:space="preserve">
    <value>Only active and / or passive modules can be updated via this form. To add a module that has not been defined to the company before, it is necessary to continue with 'Add New Module'.</value>
  </data>
  <data name="NoModuleFoundToAdd" xml:space="preserve">
    <value>No module to be added could not be found, all module definitions were made for this company. The process can be continued via the Update Existing Modules button</value>
  </data>
  <data name="NoModuleFoundToUpdate" xml:space="preserve">
    <value>There is no module available for updating. Module definition can be made via the Add New Module button</value>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>Module name</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="AddApplicationStatus" xml:space="preserve">
    <value>Add application status</value>
  </data>
  <data name="ApplicationStatusDescription" xml:space="preserve">
    <value>Application status description</value>
  </data>
  <data name="ApplicationStatusDetails" xml:space="preserve">
    <value>Application status details</value>
  </data>
  <data name="ApplicationStatusName" xml:space="preserve">
    <value>Application status name</value>
  </data>
  <data name="ApplicationStatusTypes" xml:space="preserve">
    <value>Application status types</value>
  </data>
  <data name="SendOfEmail" xml:space="preserve">
    <value>Send of email</value>
  </data>
  <data name="SendOfSms" xml:space="preserve">
    <value>Send of sms</value>
  </data>
  <data name="UpdateApplicationStatus" xml:space="preserve">
    <value>Update application status</value>
  </data>
  <data name="ActionDescription" xml:space="preserve">
    <value>Page description</value>
  </data>
  <data name="ActionDetails" xml:space="preserve">
    <value>Page details</value>
  </data>
  <data name="ActionList" xml:space="preserve">
    <value>Page list</value>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>Page title</value>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>Add page</value>
  </data>
  <data name="UpdateAction" xml:space="preserve">
    <value>Update page</value>
  </data>
  <data name="AreaName" xml:space="preserve">
    <value>Area name</value>
  </data>
  <data name="ControllerName" xml:space="preserve">
    <value>Controller name</value>
  </data>
  <data name="IsPublicAddress" xml:space="preserve">
    <value>Public address</value>
  </data>
  <data name="IsShownOnMenu" xml:space="preserve">
    <value>Show in menu</value>
  </data>
  <data name="MethodName" xml:space="preserve">
    <value>Method name</value>
  </data>
  <data name="MenuOrder" xml:space="preserve">
    <value>Menu order</value>
  </data>
  <data name="AddSubAction" xml:space="preserve">
    <value>Add sub page</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="CountryName" xml:space="preserve">
    <value>Country name</value>
  </data>
  <data name="AddBranchApplicationCountry" xml:space="preserve">
    <value>Add branch application country</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="BranchApplicationCountryDetails" xml:space="preserve">
    <value>Branch application country details</value>
  </data>
  <data name="BranchApplicationCountryList" xml:space="preserve">
    <value>Branch application country list</value>
  </data>
  <data name="UpdateBranchApplicationCountry" xml:space="preserve">
    <value>Update branch application country</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>Add user</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Phone number</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Surname</value>
  </data>
  <data name="UpdatePassword" xml:space="preserve">
    <value>Update password</value>
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value>Update user</value>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>User details</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>User list</value>
  </data>
  <data name="PageAuthority" xml:space="preserve">
    <value>Page authority</value>
  </data>
  <data name="AddUserModule" xml:space="preserve">
    <value>Add user module</value>
  </data>
  <data name="UserModuleList" xml:space="preserve">
    <value>User module list</value>
  </data>
  <data name="UserModuleDetails" xml:space="preserve">
    <value>User module details</value>
  </data>
  <data name="Module" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>User roles</value>
  </data>
  <data name="AddUserBranch" xml:space="preserve">
    <value>Add user branch</value>
  </data>
  <data name="UserBranchList" xml:space="preserve">
    <value>User branch list</value>
  </data>
  <data name="UserBranchDetails" xml:space="preserve">
    <value>User branch details</value>
  </data>
  <data name="UpdateBranchApplicationCountryVisaCategory" xml:space="preserve">
    <value>Update branch application country visa categories</value>
  </data>
  <data name="UpdateVisaCategories" xml:space="preserve">
    <value>Update visa categories</value>
  </data>
  <data name="VisaCategory" xml:space="preserve">
    <value>Type of visa</value>
  </data>
  <data name="SelectBranch" xml:space="preserve">
    <value>Select branch</value>
  </data>
  <data name="BranchNotFound" xml:space="preserve">
    <value>Branch not found</value>
  </data>
  <data name="PleaseContactYourAdministrator" xml:space="preserve">
    <value>Please contact your administrator</value>
  </data>
  <data name="AddBranchApplicationCountryExtraFee" xml:space="preserve">
    <value>Add branch application country extra fee</value>
  </data>
  <data name="BranchApplicationCountryExtraFeeList" xml:space="preserve">
    <value>Branch application country extra fee list</value>
  </data>
  <data name="BranchApplicationCountryExtraFees" xml:space="preserve">
    <value>Branch application country extra fees</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="IsAutoChecked" xml:space="preserve">
    <value>Is auto checked</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="UpdateBranchApplicationCountryExtraFee" xml:space="preserve">
    <value>Update branch application country extra fee</value>
  </data>
  <data name="ApplicationStatusOrder" xml:space="preserve">
    <value>Application status order</value>
  </data>
  <data name="OrderStatus" xml:space="preserve">
    <value>Order status</value>
  </data>
  <data name="SelectModule" xml:space="preserve">
    <value>Select module</value>
  </data>
  <data name="SelectModuleBranch" xml:space="preserve">
    <value>Select module / branch</value>
  </data>
  <data name="ModuleNotFound" xml:space="preserve">
    <value>Module not found</value>
  </data>
  <data name="ReturnUrl" xml:space="preserve">
    <value>Return url</value>
  </data>
  <data name="ApplicationEmailStatus" xml:space="preserve">
    <value>Application status email content details</value>
  </data>
  <data name="ApplicationSmsStatus" xml:space="preserve">
    <value>Application status sms content details</value>
  </data>
  <data name="EmailContent" xml:space="preserve">
    <value>Email content</value>
  </data>
  <data name="Provider" xml:space="preserve">
    <value>Provider</value>
  </data>
  <data name="SmsContent" xml:space="preserve">
    <value>Sms content</value>
  </data>
  <data name="UpdateApplicationEmailStatus" xml:space="preserve">
    <value>Update application email status content</value>
  </data>
  <data name="UpdateApplicationSmsStatus" xml:space="preserve">
    <value>Update application sms status content</value>
  </data>
  <data name="UpdateApplicationFormElements" xml:space="preserve">
    <value>Update application form elements</value>
  </data>
  <data name="FormElementName" xml:space="preserve">
    <value>Form element name</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Select all</value>
  </data>
  <data name="CompanyModuleManagement" xml:space="preserve">
    <value>Module management</value>
  </data>
  <data name="ApplicationForm" xml:space="preserve">
    <value>Application form</value>
  </data>
  <data name="SelectCountry" xml:space="preserve">
    <value>Select country</value>
  </data>
  <data name="ApplicationStep1Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ApplicationStep2BasicInformation" xml:space="preserve">
    <value>Basic information</value>
  </data>
  <data name="ApplicationStep3DocumentInformation" xml:space="preserve">
    <value>Document information</value>
  </data>
  <data name="ApplicationStep4ExtraPackages" xml:space="preserve">
    <value>Extra packages</value>
  </data>
  <data name="ApplicationStep5Finalize" xml:space="preserve">
    <value>Finalize</value>
  </data>
  <data name="ApplicantType" xml:space="preserve">
    <value>Applicant type</value>
  </data>
  <data name="ApplicationType" xml:space="preserve">
    <value>Application type</value>
  </data>
  <data name="PassportExpireDate" xml:space="preserve">
    <value>Passport expire date</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>Passport number</value>
  </data>
  <data name="FillRequiredFields" xml:space="preserve">
    <value>Fill Required Fields</value>
  </data>
  <data name="NextStep" xml:space="preserve">
    <value>Next step</value>
  </data>
  <data name="PreviousStep" xml:space="preserve">
    <value>Previous step</value>
  </data>
  <data name="AddNewApplication" xml:space="preserve">
    <value>Add new application</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Birth date</value>
  </data>
  <data name="FatherName" xml:space="preserve">
    <value>Father name</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="IdentificationNumber" xml:space="preserve">
    <value>Identification number</value>
  </data>
  <data name="MaidenName" xml:space="preserve">
    <value>Maiden name</value>
  </data>
  <data name="MotherName" xml:space="preserve">
    <value>Mother name</value>
  </data>
  <data name="Nationality" xml:space="preserve">
    <value>Nationality</value>
  </data>
  <data name="RegistrationPlaceCity" xml:space="preserve">
    <value>Registration place city</value>
  </data>
  <data name="RegistrationPlaceCountry" xml:space="preserve">
    <value>Registration place country</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="InvalidFormat" xml:space="preserve">
    <value>Invalid format</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="ClickForNewApplication" xml:space="preserve">
    <value>Click for new application</value>
  </data>
  <data name="ApplicationPassportStatus" xml:space="preserve">
    <value>Passport status</value>
  </data>
  <data name="MaritalStatus" xml:space="preserve">
    <value>Marital status</value>
  </data>
  <data name="MinimumItem" xml:space="preserve">
    <value>Minimum item</value>
  </data>
  <data name="YouWillBeRedirectedTo" xml:space="preserve">
    <value>You will be redirected to</value>
  </data>
  <data name="ApplicationList" xml:space="preserve">
    <value>Application list</value>
  </data>
  <data name="ApplicationFormElements" xml:space="preserve">
    <value>Application form elements</value>
  </data>
  <data name="InformationNotes" xml:space="preserve">
    <value>Information notes</value>
  </data>
  <data name="ApplicationTime" xml:space="preserve">
    <value>Application time</value>
  </data>
  <data name="FilteringOptions" xml:space="preserve">
    <value>Filtering Options</value>
  </data>
  <data name="ApplicationDate" xml:space="preserve">
    <value>Application date</value>
  </data>
  <data name="ApplicationNumber" xml:space="preserve">
    <value>Application number</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="AtMostTenBarcodesScan" xml:space="preserve">
    <value>At most 10 barcodes can be scanned</value>
  </data>
  <data name="BarcodeAlreadyScanned" xml:space="preserve">
    <value>Barcode already scanned</value>
  </data>
  <data name="PleaseScanBarcode" xml:space="preserve">
    <value>Please scan barcode</value>
  </data>
  <data name="RemoveAllBarcodes" xml:space="preserve">
    <value>Remove all barcodes</value>
  </data>
  <data name="AccomodationDetail" xml:space="preserve">
    <value>Accomodation Detail</value>
  </data>
  <data name="ApplicationTogether" xml:space="preserve">
    <value>Is this an application together?</value>
  </data>
  <data name="BankBalance" xml:space="preserve">
    <value>Bank balance</value>
  </data>
  <data name="CityToVisit" xml:space="preserve">
    <value>City to visit</value>
  </data>
  <data name="EntryDate" xml:space="preserve">
    <value>Entry date</value>
  </data>
  <data name="ExitDate" xml:space="preserve">
    <value>Exit date</value>
  </data>
  <data name="HasDeed" xml:space="preserve">
    <value>Does he/she have deed?</value>
  </data>
  <data name="HasEntryBan" xml:space="preserve">
    <value>Does he/she have entry ban?</value>
  </data>
  <data name="HasRelativeAbroad" xml:space="preserve">
    <value>Does he/she have relative abroad?</value>
  </data>
  <data name="Job" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="MonthlySalary" xml:space="preserve">
    <value>Monthly salary</value>
  </data>
  <data name="PersonTravelWith" xml:space="preserve">
    <value>Person traveling with</value>
  </data>
  <data name="PersonTravelWithHasVisa" xml:space="preserve">
    <value>Does the person traveling with have visa?</value>
  </data>
  <data name="ReimbursementType" xml:space="preserve">
    <value>Reimbursement type</value>
  </data>
  <data name="RelativeLocation" xml:space="preserve">
    <value>Relative's location</value>
  </data>
  <data name="TotalDay" xml:space="preserve">
    <value>Total day</value>
  </data>
  <data name="TotalYearInCompany" xml:space="preserve">
    <value>How many years does he/she work at the company?</value>
  </data>
  <data name="TotalYearInCountry" xml:space="preserve">
    <value>How many years does he/she live in the country?</value>
  </data>
  <data name="VisaEntryType" xml:space="preserve">
    <value>Visa entry type</value>
  </data>
  <data name="PreviouslyObtainedVisas" xml:space="preserve">
    <value>Previously obtained visas</value>
  </data>
  <data name="ApplicationIsCompleted" xml:space="preserve">
    <value>Application is completed</value>
  </data>
  <data name="SelectNextAction" xml:space="preserve">
    <value>Select next action</value>
  </data>
  <data name="ApplicationDetails" xml:space="preserve">
    <value>Application details</value>
  </data>
  <data name="ExportToSAP" xml:space="preserve">
    <value>Export to SAP</value>
  </data>
  <data name="PrintApplicationPage" xml:space="preserve">
    <value>Print application page</value>
  </data>
  <data name="PrintBarcode" xml:space="preserve">
    <value>Print barcode</value>
  </data>
  <data name="PrintICR" xml:space="preserve">
    <value>Print ICR</value>
  </data>
  <data name="PrintInsurancePolicy" xml:space="preserve">
    <value>Print insurance policy</value>
  </data>
  <data name="ApplicationSummary" xml:space="preserve">
    <value>Application summary</value>
  </data>
  <data name="ApplicationDocumentSummary" xml:space="preserve">
    <value>Application document summary</value>
  </data>
  <data name="ApplicationExtraFeeSummary" xml:space="preserve">
    <value>Application extra fee summary</value>
  </data>
  <data name="ApplicationNotFound" xml:space="preserve">
    <value>Application not found</value>
  </data>
  <data name="UpdateAll" xml:space="preserve">
    <value>Update all</value>
  </data>
  <data name="CreateInsurance" xml:space="preserve">
    <value>Create Insurance</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Please wait</value>
  </data>
  <data name="CorporateName" xml:space="preserve">
    <value>Corporate name</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Invoice number</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arabic</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="NotSufficientPrivileges" xml:space="preserve">
    <value>You do not have sufficient privileges for this operation</value>
  </data>
  <data name="ChooseNewApplicationStatus" xml:space="preserve">
    <value>Choose new application status</value>
  </data>
  <data name="DatedApplications" xml:space="preserve">
    <value>DatedApplications</value>
  </data>
  <data name="ForUpdatingAllApplicationStatus" xml:space="preserve">
    <value>To update the status of all applications</value>
  </data>
  <data name="PleaseClick" xml:space="preserve">
    <value>Please click</value>
  </data>
  <data name="Record" xml:space="preserve">
    <value>Record</value>
  </data>
  <data name="WillbeUpdated" xml:space="preserve">
    <value>Will be updated</value>
  </data>
  <data name="ContactInformation" xml:space="preserve">
    <value>Contact information</value>
  </data>
  <data name="CannotApplyWithThisPassportNumber" xml:space="preserve">
    <value>You cannot apply with this passport number</value>
  </data>
  <data name="ExistingInitialApplicationStatus" xml:space="preserve">
    <value>Initial application status already exists</value>
  </data>
  <data name="ApplicationStatus" xml:space="preserve">
    <value>Application status</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Application" xml:space="preserve">
    <value>Application</value>
  </data>
  <data name="BranchDailyStatisctics" xml:space="preserve">
    <value>Branch daily statistics</value>
  </data>
  <data name="BranchDistribution" xml:space="preserve">
    <value>Branch distribution</value>
  </data>
  <data name="DailyStatistics" xml:space="preserve">
    <value>Daily statistics</value>
  </data>
  <data name="WeeklyApplicationStatistics" xml:space="preserve">
    <value>Weekly application statistics</value>
  </data>
  <data name="AddNewFamilyApplication" xml:space="preserve">
    <value>Add new family application</value>
  </data>
  <data name="AddNewGroupApplication" xml:space="preserve">
    <value>Add new group application</value>
  </data>
  <data name="FamilyApplicationFormWarning" xml:space="preserve">
    <value>You are filling out a family application form</value>
  </data>
  <data name="GroupApplicationFormWarning" xml:space="preserve">
    <value>You are filling out a group application form</value>
  </data>
  <data name="RelationalApplicationNumber" xml:space="preserve">
    <value>Relational application number</value>
  </data>
  <data name="Main" xml:space="preserve">
    <value>Main</value>
  </data>
  <data name="StatusHistory" xml:space="preserve">
    <value>History of status</value>
  </data>
  <data name="UpdateBy" xml:space="preserve">
    <value>Update by</value>
  </data>
  <data name="AddPhotoBooth" xml:space="preserve">
    <value>New photo booth registration</value>
  </data>
  <data name="PhotoBoothOperations" xml:space="preserve">
    <value>Photo booth operations</value>
  </data>
  <data name="ReferenceType" xml:space="preserve">
    <value>Referencetype</value>
  </data>
  <data name="WithoutReference" xml:space="preserve">
    <value>Without reference</value>
  </data>
  <data name="WithReference" xml:space="preserve">
    <value>With reference</value>
  </data>
  <data name="CreateBarcode" xml:space="preserve">
    <value>Create barcode</value>
  </data>
  <data name="NonReferencedPhotoBoothOperations" xml:space="preserve">
    <value>None application referenced photo booth operations</value>
  </data>
  <data name="ReferencedPhotoBoothOperations" xml:space="preserve">
    <value>Application referenced photo booth operations</value>
  </data>
  <data name="CannotProcess" xml:space="preserve">
    <value>Cannot process</value>
  </data>
  <data name="AreYouSureCreateSapOrder" xml:space="preserve">
    <value>Application will be transferred to SAP, are you sure?</value>
  </data>
  <data name="BranchSapId" xml:space="preserve">
    <value>Branch sap number</value>
  </data>
  <data name="ExtraFeeSapId" xml:space="preserve">
    <value>Extra fee sap id</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PartialRefund" xml:space="preserve">
    <value>Partial refund</value>
  </data>
  <data name="ApplicationCancellationRequestSaved" xml:space="preserve">
    <value>Application cancellation request has been saved</value>
  </data>
  <data name="ApplicationCancellationReason" xml:space="preserve">
    <value>Application cancellation/partial refund reason</value>
  </data>
  <data name="ApplicationCancellationDetails" xml:space="preserve">
    <value>Application cancellation/partial refund details</value>
  </data>
  <data name="ApplicationCancellationType" xml:space="preserve">
    <value>Application cancellation type</value>
  </data>
  <data name="ApplicationCancellationRequestList" xml:space="preserve">
    <value>Application cancellation/partial refund request list</value>
  </data>
  <data name="ApplicationCancellationStatus" xml:space="preserve">
    <value>Application cancellation/partial refund status</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>Created at</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>Created by</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>Approve</value>
  </data>
  <data name="Reject" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="AreYouSureToDoThisAction" xml:space="preserve">
    <value>Are you sure to do this action?</value>
  </data>
  <data name="Piece" xml:space="preserve">
    <value>Piece</value>
  </data>
  <data name="AverageApplicationProcessTime" xml:space="preserve">
    <value>Average application process time</value>
  </data>
  <data name="DateRange" xml:space="preserve">
    <value>Date range</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="PreviousMonthVasSales" xml:space="preserve">
    <value>VAS sales for the previous month</value>
  </data>
  <data name="PreviousQuarterApplicationNumbers" xml:space="preserve">
    <value>Previous quarter completed application numbers</value>
  </data>
  <data name="PreviousWeekApplicationNumbers" xml:space="preserve">
    <value>Previous week completed application numbers</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="DailyPassportStatusValues" xml:space="preserve">
    <value>Daily passport deliveries</value>
  </data>
  <data name="DeclinedApplication" xml:space="preserve">
    <value>Declined application</value>
  </data>
  <data name="DeclinedRatio" xml:space="preserve">
    <value>Declined ratio</value>
  </data>
  <data name="FromBeginning" xml:space="preserve">
    <value>From beginning</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Insurance</value>
  </data>
  <data name="LastUpdate" xml:space="preserve">
    <value>Last update</value>
  </data>
  <data name="NumberOfActiveUsers" xml:space="preserve">
    <value>Number of active users</value>
  </data>
  <data name="PendingApproval" xml:space="preserve">
    <value>Pending approval</value>
  </data>
  <data name="Person" xml:space="preserve">
    <value>Person</value>
  </data>
  <data name="Realized" xml:space="preserve">
    <value>Realized</value>
  </data>
  <data name="AllBranches" xml:space="preserve">
    <value>All branches</value>
  </data>
  <data name="NumberOfCompletedApplications" xml:space="preserve">
    <value>Number of completed applications</value>
  </data>
  <data name="NumberOfRegisteredUser" xml:space="preserve">
    <value>Number of registered users</value>
  </data>
  <data name="TotalApplicationsBasedOnCountry" xml:space="preserve">
    <value>Total applications based on country</value>
  </data>
  <data name="BranchesDailyApplicationStats" xml:space="preserve">
    <value>Daily applications numbers of branches</value>
  </data>
  <data name="QuarterApplicationsOfCountries" xml:space="preserve">
    <value>Previous quarter country based applications</value>
  </data>
  <data name="QuarterApplicationCategoriesOfCountries" xml:space="preserve">
    <value>Previous quarter application categories</value>
  </data>
  <data name="QuarterVasOfCountries" xml:space="preserve">
    <value>Previous quarter VAS sales</value>
  </data>
  <data name="AllCountries" xml:space="preserve">
    <value>All countries</value>
  </data>
  <data name="QuarterApplicationStatusOfCountries" xml:space="preserve">
    <value>Previous quarter application statuses</value>
  </data>
  <data name="DeclinedAndDeliverd" xml:space="preserve">
    <value>Refund done for declined appointment</value>
  </data>
  <data name="ApplicationCancellationWarning" xml:space="preserve">
    <value>This process will be submitted to your administrator's approval</value>
  </data>
  <data name="EntryBannedApplications" xml:space="preserve">
    <value>Entry banned applications</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="Linkedin" xml:space="preserve">
    <value>Linkedin</value>
  </data>
  <data name="NumberOfBiometricData" xml:space="preserve">
    <value>Number of biometric data</value>
  </data>
  <data name="SocialMedia" xml:space="preserve">
    <value>Social media</value>
  </data>
  <data name="ActiveInsurance" xml:space="preserve">
    <value>Number of active insurance</value>
  </data>
  <data name="TotalInsurance" xml:space="preserve">
    <value>Number of total insurance</value>
  </data>
  <data name="FreeApplicationsOfCountries" xml:space="preserve">
    <value>Free applications received in countries</value>
  </data>
  <data name="Reporting" xml:space="preserve">
    <value>Reporting</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>Report type</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End date</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="ReportDate" xml:space="preserve">
    <value>Report date</value>
  </data>
  <data name="StaffName" xml:space="preserve">
    <value>Staff name</value>
  </data>
  <data name="StaffSurname" xml:space="preserve">
    <value>Staff surname</value>
  </data>
  <data name="TotalApplicationCount" xml:space="preserve">
    <value>Total application count</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Order number</value>
  </data>
  <data name="StaffNameSurname" xml:space="preserve">
    <value>Staff name surname</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>Bad request</value>
  </data>
  <data name="DataNotFound" xml:space="preserve">
    <value>Data not found</value>
  </data>
  <data name="ErrorOccurredInApplicationStatusCheck" xml:space="preserve">
    <value>Error occurred in application status check</value>
  </data>
  <data name="PartiallyReplaceable" xml:space="preserve">
    <value>Partially replaceable</value>
  </data>
  <data name="Static" xml:space="preserve">
    <value>Static</value>
  </data>
  <data name="Updateable" xml:space="preserve">
    <value>Updateable</value>
  </data>
  <data name="AverageBiometricAcquisition" xml:space="preserve">
    <value>Average biometric data acquisition time</value>
  </data>
  <data name="AverageWaitingTime" xml:space="preserve">
    <value>Average waiting time</value>
  </data>
  <data name="GeneralPicture" xml:space="preserve">
    <value>General view</value>
  </data>
  <data name="Hour" xml:space="preserve">
    <value>Hour</value>
  </data>
  <data name="IstizanResultTime" xml:space="preserve">
    <value>Average istizan result time</value>
  </data>
  <data name="NormalApplicationAverageResultTime" xml:space="preserve">
    <value>Average normal application result time</value>
  </data>
  <data name="Portal" xml:space="preserve">
    <value>Portal</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>Statistics</value>
  </data>
  <data name="DailyAgeRangeStats" xml:space="preserve">
    <value>Daily applicant age range stats</value>
  </data>
  <data name="DailyGenderRatio" xml:space="preserve">
    <value>Daily applicant gender ratio</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="MonthlyApplicationSummaryStats" xml:space="preserve">
    <value>Monthly application summary stats</value>
  </data>
  <data name="PreviousDayApplicationChangeStats" xml:space="preserve">
    <value>Previous day application change stats</value>
  </data>
  <data name="PreviousDayApplicationTypes" xml:space="preserve">
    <value>Application types of previous days</value>
  </data>
  <data name="PreviousDayDetailedApplicationStatus" xml:space="preserve">
    <value>Application statuses of previous day</value>
  </data>
  <data name="SatisfactionSurvey" xml:space="preserve">
    <value>Satisfaction survey</value>
  </data>
  <data name="AllAppointmentsCreatedThroughPortal" xml:space="preserve">
    <value>All appointments created through portal</value>
  </data>
  <data name="AllBranchesActivePolicyStats" xml:space="preserve">
    <value>All branches active policy stats</value>
  </data>
  <data name="AllBranchesMonthlyInsuranceStats" xml:space="preserve">
    <value>All branches monthly insurance stats</value>
  </data>
  <data name="AllBranchesQuarterInsuranceStats" xml:space="preserve">
    <value>Previous quarter all branches total insurance stats</value>
  </data>
  <data name="Appointment" xml:space="preserve">
    <value>Appointment</value>
  </data>
  <data name="DailyDetailedInsuranceStats" xml:space="preserve">
    <value>All branches daily detailed insurance stats</value>
  </data>
  <data name="DailyInsuranceAgeRangeStats" xml:space="preserve">
    <value>Daily age range stats for insurance applicants</value>
  </data>
  <data name="InstitutionTypeStats" xml:space="preserve">
    <value>Registered institution type statistics</value>
  </data>
  <data name="PortalMembershipApplicationCount" xml:space="preserve">
    <value>Portal total membership application count</value>
  </data>
  <data name="PortalRequestedMembershipCount" xml:space="preserve">
    <value>Portal total requested appointment count</value>
  </data>
  <data name="QuarterDetailedInsuranceStats" xml:space="preserve">
    <value>Previous quarter all branches detailed insurance stats</value>
  </data>
  <data name="QuarterInsuranceAgeRangeStats" xml:space="preserve">
    <value>Previous quarter age range stats for insurance applicants</value>
  </data>
  <data name="RatingsOfInstitutions" xml:space="preserve">
    <value>Ratings of institutions</value>
  </data>
  <data name="QuarterVasSales" xml:space="preserve">
    <value>Previous quarter VAS sales</value>
  </data>
  <data name="AddSlot" xml:space="preserve">
    <value>Add slot</value>
  </data>
  <data name="EnterSlotDateRange" xml:space="preserve">
    <value>Enter slot date range</value>
  </data>
  <data name="EnterSlotQuota" xml:space="preserve">
    <value>Enter slot and quota information</value>
  </data>
  <data name="NumberOfSlot" xml:space="preserve">
    <value>Number of slot</value>
  </data>
  <data name="Quota" xml:space="preserve">
    <value>Quota</value>
  </data>
  <data name="SelectBranchApplicationCountry" xml:space="preserve">
    <value>Select branch application country</value>
  </data>
  <data name="SelectSlotWeekDays" xml:space="preserve">
    <value>Select days of the week slot is valid</value>
  </data>
  <data name="SlotList" xml:space="preserve">
    <value>List slot</value>
  </data>
  <data name="SlotOpenClosed" xml:space="preserve">
    <value>Slot open/close</value>
  </data>
  <data name="ManageShortcuts" xml:space="preserve">
    <value>Manage shortcuts</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="ShortcutUpperLimitWarning" xml:space="preserve">
    <value>At most five shortcut can be selected</value>
  </data>
  <data name="GatewayMotto" xml:space="preserve">
    <value>Gateway from the World to Turkey, from Turkey to the World</value>
  </data>
  <data name="AvailableShortcuts" xml:space="preserve">
    <value>Available shortcuts</value>
  </data>
  <data name="PageReloadAfterSuccess" xml:space="preserve">
    <value>Operation is successful, menu will be refreshed</value>
  </data>
  <data name="SelectedShortcuts" xml:space="preserve">
    <value>Selected shortcuts</value>
  </data>
  <data name="BranchManagement" xml:space="preserve">
    <value>Branch management</value>
  </data>
  <data name="GoToModules" xml:space="preserve">
    <value>Go to module selection page</value>
  </data>
  <data name="NeedToSelectBranchNotification" xml:space="preserve">
    <value>Module and branch need to be selected</value>
  </data>
  <data name="SlotDate" xml:space="preserve">
    <value>Slot date</value>
  </data>
  <data name="UpdateSlot" xml:space="preserve">
    <value>Update slot</value>
  </data>
  <data name="UserNameSurname" xml:space="preserve">
    <value>User name surname</value>
  </data>
  <data name="BranchApplicationCountry" xml:space="preserve">
    <value>Branch application country</value>
  </data>
  <data name="HasBankAccount" xml:space="preserve">
    <value>Does he/she have bank account?</value>
  </data>
  <data name="ByAgeRange" xml:space="preserve">
    <value>By age range</value>
  </data>
  <data name="ByApplicationKind" xml:space="preserve">
    <value>By application kind</value>
  </data>
  <data name="ByApplicationType" xml:space="preserve">
    <value>By application type</value>
  </data>
  <data name="ByGender" xml:space="preserve">
    <value>By gender</value>
  </data>
  <data name="ByNationality" xml:space="preserve">
    <value>By nationality</value>
  </data>
  <data name="ByVisaCategory" xml:space="preserve">
    <value>by visa category</value>
  </data>
  <data name="FreeApplication" xml:space="preserve">
    <value>Free applications</value>
  </data>
  <data name="TotalApplication" xml:space="preserve">
    <value>Total applications</value>
  </data>
  <data name="CancellationReason" xml:space="preserve">
    <value>Cancellation reason</value>
  </data>
  <data name="CompanyCurrency" xml:space="preserve">
    <value>Company currency</value>
  </data>
  <data name="CompanyPrice" xml:space="preserve">
    <value>Company price</value>
  </data>
  <data name="InsuranceEndDate" xml:space="preserve">
    <value>Insurance end date</value>
  </data>
  <data name="InsuranceStartDate" xml:space="preserve">
    <value>Insurance start date</value>
  </data>
  <data name="InsuredNameSurname" xml:space="preserve">
    <value>Insured name surname</value>
  </data>
  <data name="PolicyNumber" xml:space="preserve">
    <value>Policy number</value>
  </data>
  <data name="ProviderCurrency" xml:space="preserve">
    <value>Provider currency</value>
  </data>
  <data name="ProviderLongName" xml:space="preserve">
    <value>Provider name</value>
  </data>
  <data name="ProviderPrice" xml:space="preserve">
    <value>Provider price</value>
  </data>
  <data name="ReferenceNumber" xml:space="preserve">
    <value>Reference number</value>
  </data>
  <data name="RefundDate" xml:space="preserve">
    <value>Refund date</value>
  </data>
  <data name="RefundedFeeName" xml:space="preserve">
    <value>Refunded Fee Name</value>
  </data>
  <data name="RefundedFeeAmount" xml:space="preserve">
    <value>Refunded Fee Amount</value>
  </data>
  <data name="PassportValidityPeriodLessThan180Days" xml:space="preserve">
    <value>Passport validity period cannot be less than 180 days</value>
  </data>
  <data name="CargoCount" xml:space="preserve">
    <value>Cargo count</value>
  </data>
  <data name="CargoType" xml:space="preserve">
    <value>Cargo type</value>
  </data>
  <data name="ProcessedBy" xml:space="preserve">
    <value>Processed by</value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
    <value>Total price</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Unit price</value>
  </data>
  <data name="SelectedCategory" xml:space="preserve">
    <value>Selected category</value>
  </data>
  <data name="CancellationDate" xml:space="preserve">
    <value>Cancellation date</value>
  </data>
  <data name="CancellationStatus" xml:space="preserve">
    <value>Cancellation status</value>
  </data>
  <data name="PolicyRemainingDays" xml:space="preserve">
    <value>Policy remaining days</value>
  </data>
  <data name="ReasonWithoutInsurance" xml:space="preserve">
    <value>Cause if without insurance</value>
  </data>
  <data name="ReimbursementSponsorDetail" xml:space="preserve">
    <value>Reimbursement sponsor detail</value>
  </data>
  <data name="DeleteDateTime" xml:space="preserve">
    <value>Datetime to delete</value>
  </data>
  <data name="Explanation" xml:space="preserve">
    <value>Explanation</value>
  </data>
  <data name="LastStatus" xml:space="preserve">
    <value>Last status</value>
  </data>
  <data name="RefundedPrice" xml:space="preserve">
    <value>Refunded price</value>
  </data>
  <data name="ServiceFee" xml:space="preserve">
    <value>Service fee</value>
  </data>
  <data name="VisaFee" xml:space="preserve">
    <value>Visa fee</value>
  </data>
  <data name="PcrStatus" xml:space="preserve">
    <value>Pcr status</value>
  </data>
  <data name="PcrStatusUpdate" xml:space="preserve">
    <value>Pcr status update</value>
  </data>
  <data name="BarcodeNumber" xml:space="preserve">
    <value>Barcode number</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="PaymentType" xml:space="preserve">
    <value>Payment type</value>
  </data>
  <data name="PolicyDays" xml:space="preserve">
    <value>Policy days</value>
  </data>
  <data name="PolicyStatus" xml:space="preserve">
    <value>Policy status</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="ConsularRequest" xml:space="preserve">
    <value>Consular request</value>
  </data>
  <data name="NumberOfCancelations" xml:space="preserve">
    <value>Number of cancelations</value>
  </data>
  <data name="TotalPriceOfCancelations" xml:space="preserve">
    <value>Total price of cancelations</value>
  </data>
  <data name="BranchDeliveryDate" xml:space="preserve">
    <value>Pick-up date at the office</value>
  </data>
  <data name="DeliveredPassports" xml:space="preserve">
    <value>Delivered passports</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery date</value>
  </data>
  <data name="DeliveryType" xml:space="preserve">
    <value>Delivery type</value>
  </data>
  <data name="PassportsWaitingForDelivery" xml:space="preserve">
    <value>Passports waiting for delivery</value>
  </data>
  <data name="StandByDuration" xml:space="preserve">
    <value>Standby duration</value>
  </data>
  <data name="NameSurname" xml:space="preserve">
    <value>Name surname</value>
  </data>
  <data name="UserSapId" xml:space="preserve">
    <value>User sap id</value>
  </data>
  <data name="UpdateDate" xml:space="preserve">
    <value>Update date</value>
  </data>
  <data name="UploadedRejectionDocuments" xml:space="preserve">
    <value>Uploaded rejection documents</value>
  </data>
  <data name="ApplicationId" xml:space="preserve">
    <value>Application id</value>
  </data>
  <data name="UpdateApplication" xml:space="preserve">
    <value>Update application</value>
  </data>
  <data name="ApplicationCountry" xml:space="preserve">
    <value>Application country</value>
  </data>
  <data name="CanceledQuantity" xml:space="preserve">
    <value>Canceled quantity</value>
  </data>
  <data name="CancelledTotalFee" xml:space="preserve">
    <value>Cancelled total fee</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="TotalFee" xml:space="preserve">
    <value>Total fee</value>
  </data>
  <data name="Fee" xml:space="preserve">
    <value>Fee</value>
  </data>
  <data name="Net" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="TotalCancellation" xml:space="preserve">
    <value>Total cancellation</value>
  </data>
  <data name="TotalPolicy" xml:space="preserve">
    <value>Total policy</value>
  </data>
  <data name="UtcTimeZone" xml:space="preserve">
    <value>Utc time zone</value>
  </data>
  <data name="Taxed" xml:space="preserve">
    <value>Taxed</value>
  </data>
  <data name="FeeInformation" xml:space="preserve">
    <value>Fee information</value>
  </data>
  <data name="NotDefined" xml:space="preserve">
    <value>Not defined</value>
  </data>
  <data name="VisaNo" xml:space="preserve">
    <value>Visa number</value>
  </data>
  <data name="AddCustomerCard" xml:space="preserve">
    <value>Add customer card</value>
  </data>
  <data name="CustomerCardDetails" xml:space="preserve">
    <value>Customer card details</value>
  </data>
  <data name="CustomerCardList" xml:space="preserve">
    <value>Customer card list</value>
  </data>
  <data name="UpdateCustomerCard" xml:space="preserve">
    <value>Update customer card</value>
  </data>
  <data name="ApprovalForEntryBanned" xml:space="preserve">
    <value>Entry banned applications</value>
  </data>
  <data name="QuarterApplicationCategoryOfBranches" xml:space="preserve">
    <value>Previous quarter branch based visa categories</value>
  </data>
  <data name="QuarterApplicationsOfBranches" xml:space="preserve">
    <value>Previous quarter branch based applications</value>
  </data>
  <data name="AgeRange" xml:space="preserve">
    <value>Age range</value>
  </data>
  <data name="AllBranchesMonthlyTotalInsurances" xml:space="preserve">
    <value>All branches monthly total insurances</value>
  </data>
  <data name="DailyApplicationProcessTpes" xml:space="preserve">
    <value>Daily appication kinds</value>
  </data>
  <data name="DailyApplicationTypes" xml:space="preserve">
    <value>Daily application types</value>
  </data>
  <data name="PropertyName" xml:space="preserve">
    <value>Property name</value>
  </data>
  <data name="ValueUpdateHistory" xml:space="preserve">
    <value>Value update history</value>
  </data>
  <data name="CurrentValue" xml:space="preserve">
    <value>Current value</value>
  </data>
  <data name="PreviousValue" xml:space="preserve">
    <value>Previous value</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>Updated at</value>
  </data>
  <data name="UpdatedBy" xml:space="preserve">
    <value>Updated by</value>
  </data>
  <data name="FreeApplications" xml:space="preserve">
    <value>Free applications</value>
  </data>
  <data name="QuarterAllBranches" xml:space="preserve">
    <value>Previous quarter branch details</value>
  </data>
  <data name="PreviousDayApplicationStats" xml:space="preserve">
    <value>Previous day total applications</value>
  </data>
  <data name="Free" xml:space="preserve">
    <value>Free</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="PersonIsNotAllowedToApply" xml:space="preserve">
    <value>This person is not allowed to apply</value>
  </data>
  <data name="PreviouslyRejected" xml:space="preserve">
    <value>Previously rejected</value>
  </data>
  <data name="MenuPage" xml:space="preserve">
    <value>Menu page</value>
  </data>
  <data name="ApplicationKind" xml:space="preserve">
    <value>Application kind</value>
  </data>
  <data name="Rejection" xml:space="preserve">
    <value>Rejection</value>
  </data>
  <data name="ValidInsuranceDetailsOfApplicant" xml:space="preserve">
    <value>Valid insurance details of the applicant</value>
  </data>
  <data name="IsLastItem" xml:space="preserve">
    <value>Is last item</value>
  </data>
  <data name="LastApplicationStatusItemNotFound" xml:space="preserve">
    <value>Last application status item not found</value>
  </data>
  <data name="AddNewPreApplication" xml:space="preserve">
    <value>Add new pre-application</value>
  </data>
  <data name="PreApplicationList" xml:space="preserve">
    <value>Pre-application list</value>
  </data>
  <data name="PreApplicationStep1Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="PreApplicationStep2SlotInformation" xml:space="preserve">
    <value>Slot information</value>
  </data>
  <data name="PreApplicationStep3BasicInformation" xml:space="preserve">
    <value>Basic information</value>
  </data>
  <data name="PreApplicationStep4Finalize" xml:space="preserve">
    <value>Finalize</value>
  </data>
  <data name="UpdatePreApplication" xml:space="preserve">
    <value>Update pre-application</value>
  </data>
  <data name="Applicant" xml:space="preserve">
    <value>Applicant</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="NumberOfApplicants" xml:space="preserve">
    <value>Number of applicants</value>
  </data>
  <data name="SaveApplicantAsCustomer" xml:space="preserve">
    <value>Save applicant as customer</value>
  </data>
  <data name="SearchSlot" xml:space="preserve">
    <value>Search slot</value>
  </data>
  <data name="SlotNotFound" xml:space="preserve">
    <value>Slot not found</value>
  </data>
  <data name="SlotSelectionDestroyed" xml:space="preserve">
    <value>Slot selection destroyed, new slot need to selected</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="SearchCustomerCard" xml:space="preserve">
    <value>Search customer card</value>
  </data>
  <data name="CheckRejectedStatus" xml:space="preserve">
    <value>Check rejected status</value>
  </data>
  <data name="CheckRejectedStatusPeriod" xml:space="preserve">
    <value>Period of check rejected status</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="ExistingRejectedApplicationStatus" xml:space="preserve">
    <value>Existing rejected application status</value>
  </data>
  <data name="AppointmentDetails" xml:space="preserve">
    <value>Appointment details</value>
  </data>
  <data name="AppointmentTime" xml:space="preserve">
    <value>Appointment time</value>
  </data>
  <data name="DeleteAppointment" xml:space="preserve">
    <value>Delete appointment</value>
  </data>
  <data name="UpdateAppointment" xml:space="preserve">
    <value>Update appointment</value>
  </data>
  <data name="DeleteFamilyPreApplication" xml:space="preserve">
    <value>Delete family pre application</value>
  </data>
  <data name="DeleteGroupPreApplication" xml:space="preserve">
    <value>Delete group pre application</value>
  </data>
  <data name="DeleteOnlyPreApplication" xml:space="preserve">
    <value>Delete only this appointment</value>
  </data>
  <data name="PreApplicationDetails" xml:space="preserve">
    <value>Pre application details</value>
  </data>
  <data name="AddDepartment" xml:space="preserve">
    <value>Add department</value>
  </data>
  <data name="DepartmentDetails" xml:space="preserve">
    <value>Department details</value>
  </data>
  <data name="DepartmentList" xml:space="preserve">
    <value>Department list</value>
  </data>
  <data name="DepartmentName" xml:space="preserve">
    <value>Department name</value>
  </data>
  <data name="UpdateDepartment" xml:space="preserve">
    <value>Update department</value>
  </data>
  <data name="Exception_NoRecordsFound" xml:space="preserve">
    <value>No records found</value>
  </data>
  <data name="Exception_BadRequest" xml:space="preserve">
    <value>Bad request</value>
  </data>
  <data name="Exception_ExistingRecord" xml:space="preserve">
    <value>Existing record</value>
  </data>
  <data name="Exception_ValidationFailed" xml:space="preserve">
    <value>Validation failed</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="AddBranchDepartmentFlow" xml:space="preserve">
    <value>Add branch department flow</value>
  </data>
  <data name="BranchDepartmentFlowDetails" xml:space="preserve">
    <value>Branch department flow details</value>
  </data>
  <data name="BranchDepartmentList" xml:space="preserve">
    <value>Branch department list</value>
  </data>
  <data name="ProcessOrder" xml:space="preserve">
    <value>Process order</value>
  </data>
  <data name="UpdateBranchDepartmentFlow" xml:space="preserve">
    <value>Update branch department flow</value>
  </data>
  <data name="BranchDepartmentFlow" xml:space="preserve">
    <value>Branch department flow</value>
  </data>
  <data name="AtLeastTwoDepartment" xml:space="preserve">
    <value>At least 2 department need be selected</value>
  </data>
  <data name="AvailableDepartments" xml:space="preserve">
    <value>Available departments</value>
  </data>
  <data name="SelectedDepartmants" xml:space="preserve">
    <value>Selected departmants</value>
  </data>
  <data name="MenuManagement" xml:space="preserve">
    <value>Menu management</value>
  </data>
  <data name="ManageMenuInfo1" xml:space="preserve">
    <value>The menu can be created with a maximum of 3 directories: Main Title - Sub Title - Page Link</value>
  </data>
  <data name="ManageMenuInfo2" xml:space="preserve">
    <value>Pages that do not have a new directory below are organized as links: For example: If there is no Sub Title connected to the Main Title, it will be redirected to the Main Title page link, otherwise the sub-folder will be opened</value>
  </data>
  <data name="ManageMenuInfo3" xml:space="preserve">
    <value>Components with (*) expression cannot be used as links, they can be Main Title or Sub Title with sub-directory</value>
  </data>
  <data name="ManageMenuInfo4" xml:space="preserve">
    <value>The ordering is important, the menu index will be placed in the order specified here</value>
  </data>
  <data name="ManageMenuInfo5" xml:space="preserve">
    <value>Cache information needs to be cleared in order for the edits to be reflected on the screens. This process is carried out during the day periodically</value>
  </data>
  <data name="DateTimePickerFormatView" xml:space="preserve">
    <value>dd/MM/yyyy HH:mm</value>
  </data>
  <data name="AllSelectedApplicationsNeedToHaveSameStatus" xml:space="preserve">
    <value>All selected applications need to have same status</value>
  </data>
  <data name="ForUpdatingSelectedApplicationStatus" xml:space="preserve">
    <value>To update the status of selected applications</value>
  </data>
  <data name="StatusedSelectedApplications" xml:space="preserve">
    <value>Statused selected applications</value>
  </data>
  <data name="CultureTr" xml:space="preserve">
    <value>tr-TR</value>
  </data>
  <data name="SelectStatuses" xml:space="preserve">
    <value>Select status(es)</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="ApplicationCancellation" xml:space="preserve">
    <value>Application cancellation</value>
  </data>
  <data name="Barcode" xml:space="preserve">
    <value>Barcode</value>
  </data>
  <data name="BranchApplicationStatus" xml:space="preserve">
    <value>Branch application status</value>
  </data>
  <data name="Exception_ApplicationStatusIsDifferentFromInitialStatus" xml:space="preserve">
    <value>Application cannot be updated because the application status is different from the initial status</value>
  </data>
  <data name="Exception_BarcodeAlreadyScanned" xml:space="preserve">
    <value>Barcode is already scanned</value>
  </data>
  <data name="Exception_DataIsStaticCannotBeDeleted" xml:space="preserve">
    <value>The data is static, cannot be deleted</value>
  </data>
  <data name="Exception_DataOutOfUse" xml:space="preserve">
    <value>Data is out of use</value>
  </data>
  <data name="Exception_ErrorOccurredUpdatingApplicationSapStatus" xml:space="preserve">
    <value>Applications have been sent to SAP but error occurred while updating application SAP status</value>
  </data>
  <data name="Exception_ExistingApprovedApplicationCancellation" xml:space="preserve">
    <value>The cancellation request for the relevant application has already been concluded</value>
  </data>
  <data name="Exception_ExistingPendingCancellationRequest" xml:space="preserve">
    <value>There is already a pending cancellation request for the application</value>
  </data>
  <data name="Exception_NoAvailableSlotQuota" xml:space="preserve">
    <value>There is no available slot quota during the day</value>
  </data>
  <data name="ExtraFee" xml:space="preserve">
    <value>Extra fee</value>
  </data>
  <data name="InitialApplicationStatus" xml:space="preserve">
    <value>Initial application status</value>
  </data>
  <data name="MainApplication" xml:space="preserve">
    <value>Main application</value>
  </data>
  <data name="PhotoBooth" xml:space="preserve">
    <value>Photo booth</value>
  </data>
  <data name="PreApplication" xml:space="preserve">
    <value>Pre application</value>
  </data>
  <data name="PreApplicationApplicant" xml:space="preserve">
    <value>Pre application applicant</value>
  </data>
  <data name="RelationalApplication" xml:space="preserve">
    <value>Relational application</value>
  </data>
  <data name="Slot" xml:space="preserve">
    <value>Slot</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="CompanyModule" xml:space="preserve">
    <value>Company module</value>
  </data>
  <data name="BranchApplicationCountryExtraFee" xml:space="preserve">
    <value>Branch application country extra fee</value>
  </data>
  <data name="BranchDepartment" xml:space="preserve">
    <value>Branch department</value>
  </data>
  <data name="CustomerCard" xml:space="preserve">
    <value>Customer card</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="Exception_ExceedsLicenseCountLimit" xml:space="preserve">
    <value>Exceeds license count limit</value>
  </data>
  <data name="Exception_ExistingRecordWithSameOrder" xml:space="preserve">
    <value>Existing record with same order</value>
  </data>
  <data name="Exception_NoAvailableSlotFound" xml:space="preserve">
    <value>No available slot found</value>
  </data>
  <data name="UserBranch" xml:space="preserve">
    <value>User branch</value>
  </data>
  <data name="UserModule" xml:space="preserve">
    <value>User module</value>
  </data>
  <data name="Exception_NoWaitingApplicationExtraFeeForSAP" xml:space="preserve">
    <value>No waiting application extra fee to be sent to SAP</value>
  </data>
  <data name="IcrRelation" xml:space="preserve">
    <value>Icr relation</value>
  </data>
  <data name="IcrManagement" xml:space="preserve">
    <value>Icr management</value>
  </data>
  <data name="BranchIcrDescription_1" xml:space="preserve">
    <value>It is recommended that the editor area be created using 'Table' in order to be compatible with the ICR structure.</value>
  </data>
  <data name="BranchIcrDescription_2" xml:space="preserve">
    <value>Since the design applied in the editor area will be displayed directly in the ICR,it is recommended to use 'Table'to create context</value>
  </data>
  <data name="BranchIcrDescription_3" xml:space="preserve">
    <value>Undesirable spacing-line heights etc. to appear in the ICR, might not be added into the context</value>
  </data>
  <data name="SinceBorn" xml:space="preserve">
    <value>Since born</value>
  </data>
  <data name="OnlyOneExtraFeeCanBeSelected" xml:space="preserve">
    <value>Only one extra fee can be selected in this category</value>
  </data>
  <data name="DisplayPolicy" xml:space="preserve">
    <value>Display policy</value>
  </data>
  <data name="PolicyLanguage" xml:space="preserve">
    <value>Policy language</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Select language</value>
  </data>
  <data name="BirthDateValidation" xml:space="preserve">
    <value>Birthdate must be in the past</value>
  </data>
  <data name="NotificationMail_SentCenterDueToMissingDocuments" xml:space="preserve">
    <value>The application with reference number [APPNUMBER] received on [DATE] was sent to the visa application center due to missing / additional documents.</value>
  </data>
  <data name="SelectBranches" xml:space="preserve">
    <value>Select branch(es)</value>
  </data>
  <data name="NotificationMail_CancelRequested" xml:space="preserve">
    <value>A cancellation request has been created by the staff named [REQUESTEDBY] for the application with reference number [APPNUMBER] received on [DATE], awaiting your approval.</value>
  </data>
  <data name="NotificationMail_RefundRequested" xml:space="preserve">
    <value>A partial refund request has been created by the staff named [REQUESTEDBY] for the application with reference number [APPNUMBER] received on [DATE], awaiting your approval.</value>
  </data>
  <data name="AtMostFiftyBarcodesScan" xml:space="preserve">
    <value>At most 50 barcodes can be scanned</value>
  </data>
  <data name="ApplicationNotAppliedFromBranch" xml:space="preserve">
    <value>The application does not belong to the selected branch</value>
  </data>
  <data name="AreYouSureCreateSapOrderForFamily" xml:space="preserve">
    <value>Family applications submitted so far will go to SAP. Do you confirm that all family members' applications have been completed?</value>
  </data>
  <data name="ApplicationFileType" xml:space="preserve">
    <value>File type</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>File name</value>
  </data>
  <data name="AddApplicationFile" xml:space="preserve">
    <value>Add application file</value>
  </data>
  <data name="ApplicationFiles" xml:space="preserve">
    <value>Application files</value>
  </data>
  <data name="ValidFileExtensions" xml:space="preserve">
    <value>Valid file extensions</value>
  </data>
  <data name="ValidFileSize" xml:space="preserve">
    <value>Valid file size</value>
  </data>
  <data name="ApplicationFile" xml:space="preserve">
    <value>Application file</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="EnterApplicationReferenceNumber" xml:space="preserve">
    <value>Enter the application reference number without a leading 0</value>
  </data>
  <data name="EnterPreApplicationReferenceNumber" xml:space="preserve">
    <value>Enter the appointment reference number without a leading 0</value>
  </data>
  <data name="PassportDelivery" xml:space="preserve">
    <value>Passport delivery</value>
  </data>
  <data name="QueueMatic" xml:space="preserve">
    <value>Queue matic</value>
  </data>
  <data name="ReBiometry" xml:space="preserve">
    <value>Re-Biometry</value>
  </data>
  <data name="WalkIn" xml:space="preserve">
    <value>Walk in</value>
  </data>
  <data name="AppointmentNumber" xml:space="preserve">
    <value>Appointment number</value>
  </data>
  <data name="GenerateQueueNumber" xml:space="preserve">
    <value>Generate queue number</value>
  </data>
  <data name="AtLeastOneDepartment" xml:space="preserve">
    <value>At least 1 department need be selected</value>
  </data>
  <data name="Vip" xml:space="preserve">
    <value>VIP</value>
  </data>
  <data name="CreateWalkInAppointement" xml:space="preserve">
    <value>Create walk in appointement</value>
  </data>
  <data name="CounterNumber" xml:space="preserve">
    <value>Counter number</value>
  </data>
  <data name="DepartmentCounterSelection" xml:space="preserve">
    <value>Department - counter selection</value>
  </data>
  <data name="ProcessType" xml:space="preserve">
    <value>Process type</value>
  </data>
  <data name="QueueList" xml:space="preserve">
    <value>Queue list</value>
  </data>
  <data name="WithGroupId" xml:space="preserve">
    <value>Wİth group id</value>
  </data>
  <data name="WithIndividualId" xml:space="preserve">
    <value>With individual id</value>
  </data>
  <data name="StartProcess" xml:space="preserve">
    <value>Start process</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>In progress</value>
  </data>
  <data name="DeviceNotReady" xml:space="preserve">
    <value>Device is not ready</value>
  </data>
  <data name="DeviceReady" xml:space="preserve">
    <value>Device is ready</value>
  </data>
  <data name="PassportReader" xml:space="preserve">
    <value>Passport reader</value>
  </data>
  <data name="ScanPassportAndUpdateInformation" xml:space="preserve">
    <value>Scan passport and update information</value>
  </data>
  <data name="UpdateStatus" xml:space="preserve">
    <value>Update status</value>
  </data>
  <data name="ResumeProcess" xml:space="preserve">
    <value>Resume process</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="CargoIsNotAvailable" xml:space="preserve">
    <value>Extra fee of cargo is not available for the application</value>
  </data>
  <data name="PrintCargoReceipt" xml:space="preserve">
    <value>Print cargo receipt</value>
  </data>
  <data name="WalkInAppointment" xml:space="preserve">
    <value>Walk in appointment form</value>
  </data>
  <data name="SelectBranchToContinue" xml:space="preserve">
    <value>Select branch to continue</value>
  </data>
  <data name="ByAppointment" xml:space="preserve">
    <value>By appointment</value>
  </data>
  <data name="Minute" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="StandBy" xml:space="preserve">
    <value>Stand by</value>
  </data>
  <data name="Resume" xml:space="preserve">
    <value>Resume</value>
  </data>
  <data name="QueueNumber" xml:space="preserve">
    <value>Queue number</value>
  </data>
  <data name="ShowCompleted" xml:space="preserve">
    <value>Show completed</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="Family" xml:space="preserve">
    <value>Family</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="BoundAppointment" xml:space="preserve">
    <value>Bound appointment</value>
  </data>
  <data name="ApplicantCount" xml:space="preserve">
    <value>Applicant count</value>
  </data>
  <data name="IndividualApplication" xml:space="preserve">
    <value>Individua application</value>
  </data>
  <data name="SubApplication" xml:space="preserve">
    <value>Sub application</value>
  </data>
  <data name="CreateApplication" xml:space="preserve">
    <value>Create application</value>
  </data>
  <data name="WaitingForMainApplication" xml:space="preserve">
    <value>Waiting for main application</value>
  </data>
  <data name="DisplayPassportInformation" xml:space="preserve">
    <value>Display passport information</value>
  </data>
  <data name="ShortcutPassportInformation" xml:space="preserve">
    <value>Shortcut: You can display passport information with CTRL + V keys</value>
  </data>
  <data name="PassportDataIsMissing" xml:space="preserve">
    <value>Passport data is missing</value>
  </data>
  <data name="DeletedData" xml:space="preserve">
    <value>Deleted data</value>
  </data>
  <data name="ShowDeleted" xml:space="preserve">
    <value>Show deleted</value>
  </data>
  <data name="NotificationMail_NewPreApplication" xml:space="preserve">
    <value>Appointment: [BRANCH] Branch, Date: [DATE], Hour: [TIME]</value>
  </data>
  <data name="NotificationMail_UpdatePreApplication" xml:space="preserve">
    <value>Updated Appointment: [BRANCH] Branch, Date: [DATE], Hour: [TIME]</value>
  </data>
  <data name="Postpone" xml:space="preserve">
    <value>Postpone</value>
  </data>
  <data name="AreYouSureToPostponeThisRecord" xml:space="preserve">
    <value>Are you sure to postpone this record?</value>
  </data>
  <data name="PostponeAppointment" xml:space="preserve">
    <value>Postpone appointment</value>
  </data>
  <data name="NewSlotSelection" xml:space="preserve">
    <value>New slot selection</value>
  </data>
  <data name="AppointmentNumberIs" xml:space="preserve">
    <value>Appointment number is</value>
  </data>
  <data name="BranchApplicationCountryFile" xml:space="preserve">
    <value>Branch application country file</value>
  </data>
  <data name="BranchApplicationCountryFileType" xml:space="preserve">
    <value>Branch application country file type</value>
  </data>
  <data name="AddNewFile" xml:space="preserve">
    <value>Add new file</value>
  </data>
  <data name="FileManagement" xml:space="preserve">
    <value>File management</value>
  </data>
  <data name="FileType" xml:space="preserve">
    <value>File type</value>
  </data>
  <data name="ApplicationCancelledOrDeleted" xml:space="preserve">
    <value>Application is deleted or canceled</value>
  </data>
  <data name="IsApplicationStatusHidden" xml:space="preserve">
    <value>Hide application status</value>
  </data>
  <data name="IsApplicationUpdateAllowed" xml:space="preserve">
    <value>Allow application update</value>
  </data>
  <data name="IsNewApplicationWithSamePassportNumberBlocked" xml:space="preserve">
    <value>Block new application with same passport number</value>
  </data>
  <data name="BranchApplicationStatusItemNotFound" xml:space="preserve">
    <value>Branch application status item not found</value>
  </data>
  <data name="Ratio" xml:space="preserve">
    <value>Ratio</value>
  </data>
  <data name="SalesFee" xml:space="preserve">
    <value>Sales fee</value>
  </data>
  <data name="CustomerCardNote" xml:space="preserve">
    <value>Customer card note</value>
  </data>
  <data name="CustomerCardNotes" xml:space="preserve">
    <value>Customer card notes</value>
  </data>
  <data name="AddCustomerCardNote" xml:space="preserve">
    <value>Add customer card note</value>
  </data>
  <data name="Note" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="AppointmentManagement" xml:space="preserve">
    <value>Appointment management</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>Age</value>
  </data>
  <data name="AppointmentDate" xml:space="preserve">
    <value>Appointment date</value>
  </data>
  <data name="AppointmentHour" xml:space="preserve">
    <value>Appointment hour</value>
  </data>
  <data name="AppointmentReport" xml:space="preserve">
    <value>Appointment report</value>
  </data>
  <data name="CancelledReport" xml:space="preserve">
    <value>Cancelled report</value>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>Generate report</value>
  </data>
  <data name="AgencyType" xml:space="preserve">
    <value>Agency type</value>
  </data>
  <data name="AgencyTypeList" xml:space="preserve">
    <value>Agency type list</value>
  </data>
  <data name="AddAgencyType" xml:space="preserve">
    <value>Add agency type</value>
  </data>
  <data name="AgencyTypeDetails" xml:space="preserve">
    <value>Agency type detail</value>
  </data>
  <data name="AgencyTypeName" xml:space="preserve">
    <value>Agency type name</value>
  </data>
  <data name="UpdateAgencyType" xml:space="preserve">
    <value>Update agency type</value>
  </data>
  <data name="AgencyTypeDescription" xml:space="preserve">
    <value>Agency type description</value>
  </data>
  <data name="AgencyTypeFile" xml:space="preserve">
    <value>Agency type file</value>
  </data>
  <data name="AddAgency" xml:space="preserve">
    <value>Add agency</value>
  </data>
  <data name="Agency" xml:space="preserve">
    <value>Agency</value>
  </data>
  <data name="AgencyAddress" xml:space="preserve">
    <value>Agency address</value>
  </data>
  <data name="AgencyAuthorizedPersonName" xml:space="preserve">
    <value>Agency authorized person name</value>
  </data>
  <data name="AgencyAuthorizedPersonSurname" xml:space="preserve">
    <value>Agency authorized person surname</value>
  </data>
  <data name="AgencyCategory" xml:space="preserve">
    <value>Agency Category</value>
  </data>
  <data name="AgencyCityName" xml:space="preserve">
    <value>Agency city</value>
  </data>
  <data name="AgencyCountryName" xml:space="preserve">
    <value>Agency Country Name</value>
  </data>
  <data name="AgencyDescription" xml:space="preserve">
    <value>Agency description</value>
  </data>
  <data name="AgencyDetails" xml:space="preserve">
    <value>Agency detail</value>
  </data>
  <data name="AgencyEmail" xml:space="preserve">
    <value>Agency e-mail</value>
  </data>
  <data name="AgencyList" xml:space="preserve">
    <value>Agency List</value>
  </data>
  <data name="AgencyName" xml:space="preserve">
    <value>Agency name</value>
  </data>
  <data name="AgencyTelephone" xml:space="preserve">
    <value>Agency telephone</value>
  </data>
  <data name="AgencyTypeFileCount" xml:space="preserve">
    <value>Agency type files count</value>
  </data>
  <data name="UpdateAgency" xml:space="preserve">
    <value>Update agency</value>
  </data>
  <data name="AgencyTypeFileList" xml:space="preserve">
    <value>Agency Type File List</value>
  </data>
  <data name="NotificationMail_LinkForNewPassword" xml:space="preserve">
    <value>Please click to set a new password. [LINK]</value>
  </data>
  <data name="SlotType" xml:space="preserve">
    <value>Slot type</value>
  </data>
  <data name="AgencyUser" xml:space="preserve">
    <value>Agency user</value>
  </data>
  <data name="UserAccountIsNotActive" xml:space="preserve">
    <value>User account is not active</value>
  </data>
  <data name="ManageAgencyTypeFile" xml:space="preserve">
    <value>Manage file type</value>
  </data>
  <data name="AuthorizedUserAlreadyExists" xml:space="preserve">
    <value>Auhtorized user alreadry exists</value>
  </data>
  <data name="AddAgencyUser" xml:space="preserve">
    <value>Add agency user</value>
  </data>
  <data name="AgencyUserList" xml:space="preserve">
    <value>Agency user list</value>
  </data>
  <data name="Authorized" xml:space="preserve">
    <value>Authorized</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="UpdateAgencyUser" xml:space="preserve">
    <value>Update agency user</value>
  </data>
  <data name="UserAgencyDetails" xml:space="preserve">
    <value>Agency user details</value>
  </data>
  <data name="Exception_ApplicationUpdateIsNotAllowedForThisApplicationStatus" xml:space="preserve">
    <value>Application update is not allowed for this application status</value>
  </data>
  <data name="Exception_NewApplicationWithSamePassportNumberIsNotAllowedForThisApplicationStatus" xml:space="preserve">
    <value>New application with same passport number is not allowed for this application status</value>
  </data>
  <data name="VasType" xml:space="preserve">
    <value>VAS type</value>
  </data>
  <data name="NotificationMail_InsurancePolicy" xml:space="preserve">
    <value>Your insurance policy is attached.</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>Send email</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="AgencyFile" xml:space="preserve">
    <value>Agency file</value>
  </data>
  <data name="AgencyFileList" xml:space="preserve">
    <value>Agency file list</value>
  </data>
  <data name="UploadStatus" xml:space="preserve">
    <value>Yükleme Durumu</value>
  </data>
  <data name="ConfirmUpdateAllStatus" xml:space="preserve">
    <value>To perform a batch update, the update all option must be selected. Applications whose status has changed will be updated, do you want to continue?</value>
  </data>
  <data name="AllowUpdate" xml:space="preserve">
    <value>Allow update</value>
  </data>
  <data name="BranchApplicationCountryExtraFeeCost" xml:space="preserve">
    <value>Extra fee cost</value>
  </data>
  <data name="AddUpdateBranchApplicationCountryExtraFeeCost" xml:space="preserve">
    <value>Add/Update Cost</value>
  </data>
  <data name="TaxRate" xml:space="preserve">
    <value>Tax rate</value>
  </data>
  <data name="BranchApplicationCountryExtraFeeCommission" xml:space="preserve">
    <value>Branch application country extra fee commission</value>
  </data>
  <data name="CommissionType" xml:space="preserve">
    <value>Commission type</value>
  </data>
  <data name="AddUpdateBranchApplicationCountryExtraFeeCommission" xml:space="preserve">
    <value>Add/Update commission</value>
  </data>
  <data name="IsVisaUsed" xml:space="preserve">
    <value>Visa is used</value>
  </data>
  <data name="VisaFromDate" xml:space="preserve">
    <value>Visa from date</value>
  </data>
  <data name="VisaUntilDate" xml:space="preserve">
    <value>Visa until date</value>
  </data>
  <data name="AgencySlotMatchError" xml:space="preserve">
    <value>Applicant and slot types must be matched by agency selection</value>
  </data>
  <data name="PhotoBoothsWithoutReference" xml:space="preserve">
    <value>PhotoBooths without reference</value>
  </data>
  <data name="ReferencedPhotoBooth" xml:space="preserve">
    <value>Application referenced photo booth</value>
  </data>
  <data name="AppointmentNotActivated" xml:space="preserve">
    <value>Appointment not activated</value>
  </data>
  <data name="FeeCurrencyForCompany" xml:space="preserve">
    <value>Fee currency for company</value>
  </data>
  <data name="FeeForCompany" xml:space="preserve">
    <value>Fee for company</value>
  </data>
  <data name="IsAgencyApplication" xml:space="preserve">
    <value>Agency application</value>
  </data>
  <data name="SftpConfiguration" xml:space="preserve">
    <value>Sftp configuration</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="ApplicationData" xml:space="preserve">
    <value>Application data</value>
  </data>
  <data name="ApplicationDataContact" xml:space="preserve">
    <value>Application d ata contact</value>
  </data>
  <data name="ApplicationDataDemographic" xml:space="preserve">
    <value>Application data demographic</value>
  </data>
  <data name="ApplicationDataTravel" xml:space="preserve">
    <value>Application data travel</value>
  </data>
  <data name="ApplicationDataTravelDetail" xml:space="preserve">
    <value>Application data travel detail</value>
  </data>
  <data name="QueueMaticPlaylistId" xml:space="preserve">
    <value>Queue matic playlist Id</value>
  </data>
  <data name="TurkeyVisaApplicationCenter" xml:space="preserve">
    <value>Turkey Visa Application Center</value>
  </data>
  <data name="Recall" xml:space="preserve">
    <value>Recall</value>
  </data>
  <data name="TravelDocumentType" xml:space="preserve">
    <value>Travel document type</value>
  </data>
  <data name="PurposeOfTrip" xml:space="preserve">
    <value>Purpose of trip</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Demographic" xml:space="preserve">
    <value>Demographic</value>
  </data>
  <data name="Travel" xml:space="preserve">
    <value>Travel</value>
  </data>
  <data name="TravelDetail" xml:space="preserve">
    <value>Travel details</value>
  </data>
  <data name="DataInformationSaved" xml:space="preserve">
    <value>Data information saved</value>
  </data>
  <data name="SaveAndContinueToTravel" xml:space="preserve">
    <value>Save and continue to travel page</value>
  </data>
  <data name="SaveAndContinueToTravelDetails" xml:space="preserve">
    <value>Save and continue to travel details</value>
  </data>
  <data name="IssuingAuthority" xml:space="preserve">
    <value>Issuing authority</value>
  </data>
  <data name="IssuingState" xml:space="preserve">
    <value>Issuing state</value>
  </data>
  <data name="NotApplicable" xml:space="preserve">
    <value>Not applicable</value>
  </data>
  <data name="PassportIssueDate" xml:space="preserve">
    <value>Passport issue date</value>
  </data>
  <data name="ValidUntil" xml:space="preserve">
    <value>Valid until</value>
  </data>
  <data name="ArrivalDate" xml:space="preserve">
    <value>Arrival date</value>
  </data>
  <data name="DepartureDate" xml:space="preserve">
    <value>Departure date</value>
  </data>
  <data name="DurationStayInDays" xml:space="preserve">
    <value>Duration stay in days</value>
  </data>
  <data name="MeansOfTransport" xml:space="preserve">
    <value>Means of transport</value>
  </data>
  <data name="SaveAndContinueToDemographic" xml:space="preserve">
    <value>Save and continue to demographic</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Postal code</value>
  </data>
  <data name="QuestionTextExpensesCoverBy" xml:space="preserve">
    <value>Who will cover your travel expenses and/or living costs during your stay in Turkey?</value>
  </data>
  <data name="QuestionTextIsHealthInsuranceExisting" xml:space="preserve">
    <value>Do you have travel and/or health insurance valid throughout your visit to Turkey?</value>
  </data>
  <data name="SaveAndContinueToContact" xml:space="preserve">
    <value>Save and continue to contact</value>
  </data>
  <data name="BirthCountry" xml:space="preserve">
    <value>Birth country</value>
  </data>
  <data name="BirthPlace" xml:space="preserve">
    <value>Birth place</value>
  </data>
  <data name="Occupation" xml:space="preserve">
    <value>Occupation</value>
  </data>
  <data name="OtherSurname" xml:space="preserve">
    <value>Other surname</value>
  </data>
  <data name="SaveAndSendToQualityCheck" xml:space="preserve">
    <value>Save and send to quality check</value>
  </data>
  <data name="TitleCurrentEmployer" xml:space="preserve">
    <value>Current employer / educational institution information</value>
  </data>
  <data name="Announcement" xml:space="preserve">
    <value>Announcement</value>
  </data>
  <data name="AnnouncementList" xml:space="preserve">
    <value>Announcement list</value>
  </data>
  <data name="AddAnnouncement" xml:space="preserve">
    <value>Add announcement</value>
  </data>
  <data name="AnnouncementDetails" xml:space="preserve">
    <value>Announcement details</value>
  </data>
  <data name="UpdateAnnouncement" xml:space="preserve">
    <value>Update announcement</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="ReadStatus" xml:space="preserve">
    <value>Read status</value>
  </data>
  <data name="MarkAsRead" xml:space="preserve">
    <value>Mark as read</value>
  </data>
  <data name="AnnouncementHistory" xml:space="preserve">
    <value>Announcement history</value>
  </data>
  <data name="Announcements" xml:space="preserve">
    <value>Announcements</value>
  </data>
  <data name="AppliedDocument" xml:space="preserve">
    <value>Applied document</value>
  </data>
  <data name="CrimeDate" xml:space="preserve">
    <value>Crime date</value>
  </data>
  <data name="DeportationDate" xml:space="preserve">
    <value>Deportation date</value>
  </data>
  <data name="DeportationInstitution" xml:space="preserve">
    <value>Deportation institution</value>
  </data>
  <data name="DeportationReason" xml:space="preserve">
    <value>Deportation reason</value>
  </data>
  <data name="DetailOfCrime" xml:space="preserve">
    <value>Detail of crime</value>
  </data>
  <data name="PointOfEntry" xml:space="preserve">
    <value>Point of entry</value>
  </data>
  <data name="ReasonOfOverStay" xml:space="preserve">
    <value>Reason of over stay</value>
  </data>
  <data name="RefusingAuthority" xml:space="preserve">
    <value>Refusing authority</value>
  </data>
  <data name="RejectedDate" xml:space="preserve">
    <value>Rejected date</value>
  </data>
  <data name="RevocationDate" xml:space="preserve">
    <value>Revocation date</value>
  </data>
  <data name="RevocationReason" xml:space="preserve">
    <value>Revocation reason</value>
  </data>
  <data name="RevokingAuthority" xml:space="preserve">
    <value>Revoking authority</value>
  </data>
  <data name="TypeOfCrime" xml:space="preserve">
    <value>Type of crime</value>
  </data>
  <data name="EnterUpdateData" xml:space="preserve">
    <value>Enter / update data</value>
  </data>
  <data name="AddApplicationNote" xml:space="preserve">
    <value>Add application note</value>
  </data>
  <data name="ApplicationNotes" xml:space="preserve">
    <value>Application notes</value>
  </data>
  <data name="ApplicationNote" xml:space="preserve">
    <value>Application note</value>
  </data>
  <data name="ApplicationNotesExist" xml:space="preserve">
    <value>Application notes exist</value>
  </data>
  <data name="AddDamageRejectionFile" xml:space="preserve">
    <value>Add damage / rejection file</value>
  </data>
  <data name="LastProcessedBy" xml:space="preserve">
    <value>Last updated by</value>
  </data>
  <data name="QualityCheck" xml:space="preserve">
    <value>Quality check</value>
  </data>
  <data name="QualityCheckDetails" xml:space="preserve">
    <value>Quality check details</value>
  </data>
  <data name="QualityCheckList" xml:space="preserve">
    <value>Quaility check list</value>
  </data>
  <data name="Check" xml:space="preserve">
    <value>Check</value>
  </data>
  <data name="DataNumber" xml:space="preserve">
    <value>Data number</value>
  </data>
  <data name="ReadonlyNotAllowToMakeChanges" xml:space="preserve">
    <value>You are in readonly model, not allowed to make changes</value>
  </data>
  <data name="ConfirmQualityCheck" xml:space="preserve">
    <value>Please confirm sending quality check</value>
  </data>
  <data name="AllowPassiveDeletedApplications" xml:space="preserve">
    <value>Allow passive / deleted applications</value>
  </data>
  <data name="EasyUse" xml:space="preserve">
    <value>Easy use</value>
  </data>
  <data name="Deleted" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="InsuranceNumber" xml:space="preserve">
    <value>Insurance number</value>
  </data>
  <data name="ResetApplicationStatus" xml:space="preserve">
    <value>Reset application status</value>
  </data>
  <data name="ChangesNotAllowedDueToQCStage" xml:space="preserve">
    <value>Changes not allowed due to QC stage</value>
  </data>
  <data name="DataCompletedSendToQC" xml:space="preserve">
    <value>Data completed, send to QC</value>
  </data>
  <data name="SendToQC" xml:space="preserve">
    <value>Send to QC</value>
  </data>
  <data name="AreYouSureToRejectThisRecord" xml:space="preserve">
    <value>Are you sure to reject this record</value>
  </data>
  <data name="NotificationMail_AddAgencyUser" xml:space="preserve">
    <value>Your user registration has been created, your temporary password is [PASSWORD]</value>
  </data>
  <data name="AreYouSureToActivateThisRecord" xml:space="preserve">
    <value>Are you sure to activate this record?</value>
  </data>
  <data name="ActivateFamilyPreApplication" xml:space="preserve">
    <value>Activate family pre application</value>
  </data>
  <data name="ActivateGroupPreApplication" xml:space="preserve">
    <value>Activate group pre application</value>
  </data>
  <data name="ActivatePreApplication" xml:space="preserve">
    <value>Activate pre application</value>
  </data>
  <data name="BackToInfoDesk" xml:space="preserve">
    <value>Back to info desk</value>
  </data>
  <data name="MaxAppointmentDay" xml:space="preserve">
    <value>Max appointment day</value>
  </data>
  <data name="DeleteSlot" xml:space="preserve">
    <value>Delete slot</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerList" xml:space="preserve">
    <value>Customer list</value>
  </data>
  <data name="PreviousApplications" xml:space="preserve">
    <value>Previous applications</value>
  </data>
  <data name="PreviousPreApplications" xml:space="preserve">
    <value>Previous preapplications</value>
  </data>
  <data name="TotalSalesCount" xml:space="preserve">
    <value>Total sales count</value>
  </data>
  <data name="ApplicationSearchWithPassportNumber" xml:space="preserve">
    <value>Application search with passport number</value>
  </data>
  <data name="EnterPassportNumber" xml:space="preserve">
    <value>Enter passport number</value>
  </data>
  <data name="Readers" xml:space="preserve">
    <value>Readers</value>
  </data>
  <data name="B2BUser" xml:space="preserve">
    <value>B2B user</value>
  </data>
  <data name="B2CUser" xml:space="preserve">
    <value>B2C user</value>
  </data>
  <data name="ConfirmUpdateApplicationNotToSendToSap" xml:space="preserve">
    <value>The application has not been sent to SAP yet, do you want to continue updating?</value>
  </data>
  <data name="UploadedDocuments" xml:space="preserve">
    <value>Uploaded documents</value>
  </data>
  <data name="ShowInICR" xml:space="preserve">
    <value>Show in ICR</value>
  </data>
  <data name="TakenNotesWillBeSeenByAgency" xml:space="preserve">
    <value>Taken notes will be seen by the agency</value>
  </data>
  <data name="AuthorizedPersonEmail" xml:space="preserve">
    <value>Authorized person email</value>
  </data>
  <data name="AuthorizedPersonPhoneNumber" xml:space="preserve">
    <value>Authorized person phone number</value>
  </data>
  <data name="AuthorizedPersonPosition" xml:space="preserve">
    <value>Authorized person position</value>
  </data>
  <data name="NearestAvailableSlotTime" xml:space="preserve">
    <value>Nearest available slot</value>
  </data>
  <data name="NotificationMail_AddAgencyUserFile" xml:space="preserve">
    <value>Your application has been received. Upload your files through the portal to complete your application. [FILE]</value>
  </data>
  <data name="ShowPast" xml:space="preserve">
    <value>Show past</value>
  </data>
  <data name="AllItemsTaxed" xml:space="preserve">
    <value>All items taxed</value>
  </data>
  <data name="InsuranceExists" xml:space="preserve">
    <value>Insurance exists</value>
  </data>
  <data name="IsSpouseTurkishCitizen" xml:space="preserve">
    <value>Is the spouse a Turkish citizen?</value>
  </data>
  <data name="SpouseNationalNumber" xml:space="preserve">
    <value>Spouse national number</value>
  </data>
  <data name="BankPos" xml:space="preserve">
    <value>Bank pos</value>
  </data>
  <data name="AddBankPos" xml:space="preserve">
    <value>Add bank pos</value>
  </data>
  <data name="BankPosList" xml:space="preserve">
    <value>Bank pos list</value>
  </data>
  <data name="Bank" xml:space="preserve">
    <value>Bank</value>
  </data>
  <data name="BankPosDetails" xml:space="preserve">
    <value>Bank pos details</value>
  </data>
  <data name="MerchantCode" xml:space="preserve">
    <value>Merchant code</value>
  </data>
  <data name="TerminalCode" xml:space="preserve">
    <value>Terminal code</value>
  </data>
  <data name="UpdateBankPos" xml:space="preserve">
    <value>Update bank pos</value>
  </data>
  <data name="EncryptionKey" xml:space="preserve">
    <value>Encryption key</value>
  </data>
  <data name="Payment3dConfirmUrl" xml:space="preserve">
    <value>Payment confirm url (3d)</value>
  </data>
  <data name="Payment3dUrl" xml:space="preserve">
    <value>Payment url (3d)</value>
  </data>
  <data name="PaymentUrl" xml:space="preserve">
    <value>Payment url</value>
  </data>
  <data name="StoreKey" xml:space="preserve">
    <value>Store key</value>
  </data>
  <data name="Is3dEnabled" xml:space="preserve">
    <value>3d payment enabled</value>
  </data>
  <data name="Channel" xml:space="preserve">
    <value>Channel</value>
  </data>
  <data name="BankPosInstallmentList" xml:space="preserve">
    <value>Bank pos installment list</value>
  </data>
  <data name="GetPosInstallment" xml:space="preserve">
    <value>Get bank pos installments</value>
  </data>
  <data name="CommissionRate" xml:space="preserve">
    <value>Commission rate</value>
  </data>
  <data name="InstallmentCount" xml:space="preserve">
    <value>Installment count</value>
  </data>
  <data name="RejectionSummaryPage" xml:space="preserve">
    <value>Rejection summary page</value>
  </data>
  <data name="RejectedEndDate" xml:space="preserve">
    <value>Rejected end date</value>
  </data>
  <data name="RejectedStartDate" xml:space="preserve">
    <value>Rejected start date</value>
  </data>
  <data name="Data" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="Policy" xml:space="preserve">
    <value>Policy</value>
  </data>
  <data name="AppointmentOutOfDate" xml:space="preserve">
    <value>Appointment out of date</value>
  </data>
  <data name="EarlyAppointmentProcess" xml:space="preserve">
    <value>Appointment time is not come yet. The appointment date is: </value>
  </data>
  <data name="DoYouWantToUpdatePreApplicationDetails" xml:space="preserve">
    <value>Do you want to update pre-application details?</value>
  </data>
  <data name="AppointmentStatus" xml:space="preserve">
    <value>Appointment status</value>
  </data>
  <data name="BiometryProcessTimeLength" xml:space="preserve">
    <value>Biometrics process time length</value>
  </data>
  <data name="BiometryWaitingTimeLength" xml:space="preserve">
    <value>Biometrics waiting time length</value>
  </data>
  <data name="CashierProcessTimeLength" xml:space="preserve">
    <value>Cashier process time length</value>
  </data>
  <data name="CashierWaitingTimeLength" xml:space="preserve">
    <value>Cashier waiting time length</value>
  </data>
  <data name="OnHoldStatus" xml:space="preserve">
    <value>On hold status</value>
  </data>
  <data name="ProcessTimeLength" xml:space="preserve">
    <value>Process time length</value>
  </data>
  <data name="SubmissionProcessTimeLength" xml:space="preserve">
    <value>Submission process time length</value>
  </data>
  <data name="SubmissionWaitingTimeLength" xml:space="preserve">
    <value>Submission waiting time length</value>
  </data>
  <data name="TimeLengthOfComplete" xml:space="preserve">
    <value>Time length of complete</value>
  </data>
  <data name="TokenCreatedBy" xml:space="preserve">
    <value>Token created by</value>
  </data>
  <data name="WaitingTimeLength" xml:space="preserve">
    <value>Waiting time length</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="AddInventory" xml:space="preserve">
    <value>Add inventory</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="Definition" xml:space="preserve">
    <value>Definition</value>
  </data>
  <data name="DefinitionuStatus" xml:space="preserve">
    <value>Definitionu status</value>
  </data>
  <data name="InventoryDetails" xml:space="preserve">
    <value>Inventory details</value>
  </data>
  <data name="InventoryList" xml:space="preserve">
    <value>Inventory list</value>
  </data>
  <data name="InventoryType" xml:space="preserve">
    <value>Inventory type</value>
  </data>
  <data name="InventoryUpdate" xml:space="preserve">
    <value>Inventory date</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="SDKAddress" xml:space="preserve">
    <value>SDK address</value>
  </data>
  <data name="SDKVersion" xml:space="preserve">
    <value>SDK version</value>
  </data>
  <data name="SerialNumber" xml:space="preserve">
    <value>Serial number</value>
  </data>
  <data name="UpdateInventory" xml:space="preserve">
    <value>Update inventory</value>
  </data>
  <data name="HealthInstitution" xml:space="preserve">
    <value>Health institution</value>
  </data>
  <data name="DeleteToken" xml:space="preserve">
    <value>Delete token</value>
  </data>
  <data name="PremiumLounge" xml:space="preserve">
    <value>Premium lounge</value>
  </data>
  <data name="CallNext" xml:space="preserve">
    <value>Call next</value>
  </data>
  <data name="InOrder" xml:space="preserve">
    <value>In order</value>
  </data>
  <data name="WaitingForCall" xml:space="preserve">
    <value>Waiting for call</value>
  </data>
  <data name="ClientDevice" xml:space="preserve">
    <value>Client device</value>
  </data>
  <data name="AddClientDevice" xml:space="preserve">
    <value>Add client device</value>
  </data>
  <data name="ClientDeviceDetails" xml:space="preserve">
    <value>Client device details</value>
  </data>
  <data name="ClientDeviceList" xml:space="preserve">
    <value>Client device list</value>
  </data>
  <data name="UpdateClientDevice" xml:space="preserve">
    <value>Update client device</value>
  </data>
  <data name="ClientDeviceInventory" xml:space="preserve">
    <value>Client device inventory</value>
  </data>
  <data name="Exception_AlreadyInProgress" xml:space="preserve">
    <value>Already in progress</value>
  </data>
  <data name="ShowInSummary" xml:space="preserve">
    <value>Show in summary</value>
  </data>
  <data name="PhotoDate" xml:space="preserve">
    <value>Photo Date</value>
  </data>
  <data name="AdressBarcode" xml:space="preserve">
    <value>Adress Barcode</value>
  </data>
  <data name="ScanCycleReport" xml:space="preserve">
    <value>Scan Cycle Report</value>
  </data>
  <data name="ChangeSlotSelection" xml:space="preserve">
    <value>Change Slot Selection</value>
  </data>
  <data name="RemaningSlotQuota" xml:space="preserve">
    <value>Appointments will be made for the appropriate slots for the selected day, as the slot is more than the quota amount of the PreApplication preferences</value>
  </data>
  <data name="DaySlotNotFound" xml:space="preserve">
    <value>No available slots were found during the selected day</value>
  </data>
  <data name="PriorApplicationStatus" xml:space="preserve">
    <value>Prior Application Status</value>
  </data>
  <data name="AddPriorApplicationStatus" xml:space="preserve">
    <value>Add prior application status</value>
  </data>
  <data name="ExistingBranchPriorStatus" xml:space="preserve">
    <value>Existing prior application status this branch</value>
  </data>
  <data name="UpdatePriorApplicationStatus" xml:space="preserve">
    <value>Update prior application status</value>
  </data>
  <data name="ExceptionPriorApplicationStatus" xml:space="preserve">
    <value>Application status update order is incorrect</value>
  </data>
  <data name="AllowDeniedPassport" xml:space="preserve">
    <value>Allow Denied Passport</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="ThisPassportIsNotBlocked" xml:space="preserve">
    <value>This passport is not blocked</value>
  </data>
  <data name="BySamePassportNumber" xml:space="preserve">
    <value>By same passport number</value>
  </data>
  <data name="BySamePhoneNumber" xml:space="preserve">
    <value>By same phone number</value>
  </data>
  <data name="ProcedureControlReport" xml:space="preserve">
    <value>Procedure control report</value>
  </data>
  <data name="FamilyApplicationReport" xml:space="preserve">
    <value>Family Report</value>
  </data>
  <data name="LastUpdatedAt" xml:space="preserve">
    <value>Last Updated At</value>
  </data>
  <data name="ShowUpdated" xml:space="preserve">
    <value>Show Updated</value>
  </data>
  <data name="UpdatedInformation" xml:space="preserve">
    <value>Updated Information</value>
  </data>
  <data name="UpdatedReport" xml:space="preserve">
    <value>Updated Report</value>
  </data>
  <data name="PrintEntryInformationFormForTurkey" xml:space="preserve">
    <value>Print Entry Information Form for Turkey</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="RemainingBlockTime" xml:space="preserve">
    <value>Remaining Block Time</value>
  </data>
  <data name="DocumentEditing" xml:space="preserve">
    <value>Document Editing</value>
  </data>
  <data name="RefundedFeeDate" xml:space="preserve">
    <value>Refund Date</value>
  </data>
  <data name="CorporateNameArabic" xml:space="preserve">
    <value>Corporate name (arabic)</value>
  </data>
  <data name="InvoiceNumberArabic" xml:space="preserve">
    <value>Invoice number (arabic)</value>
  </data>
  <data name="BranchAddressArabic" xml:space="preserve">
    <value>Branch address (arabic)</value>
  </data>
  <data name="BranchMissionArabic" xml:space="preserve">
    <value>Branch mission (arabic)</value>
  </data>
  <data name="IcrNoteArabic" xml:space="preserve">
    <value>ICR note (Arabic)</value>
  </data>
  <data name="IcrNoteEnglish" xml:space="preserve">
    <value>ICR note (English)</value>
  </data>
  <data name="ApplicantRelationship" xml:space="preserve">
    <value>Applicant Relationship</value>
  </data>
  <data name="MoreThanOneHusbandOrWifeCannotBeEntered" xml:space="preserve">
    <value>More than one husband or wife cannot be entered</value>
  </data>
  <data name="VisaDecision" xml:space="preserve">
    <value>Visa decision</value>
  </data>
  <data name="FileCategory" xml:space="preserve">
    <value>File category</value>
  </data>
  <data name="NumberOfEntry" xml:space="preserve">
    <value>Number of entry</value>
  </data>
  <data name="VisaDuration" xml:space="preserve">
    <value>Visa duration</value>
  </data>
  <data name="AddApplicationVisaDecision" xml:space="preserve">
    <value>Add application visa decision</value>
  </data>
  <data name="RecieveTimeAtVac" xml:space="preserve">
    <value>Recieve Time At Vac</value>
  </data>
  <data name="AppointmentCreationDate" xml:space="preserve">
    <value>Appointment creation date</value>
  </data>
  <data name="BasePrice" xml:space="preserve">
    <value>Base price</value>
  </data>
  <data name="ServiceTax" xml:space="preserve">
    <value>Service tax</value>
  </data>
  <data name="AddOtherApplicationFiles" xml:space="preserve">
    <value>Add other application files</value>
  </data>
  <data name="PreviousJobOfApplicant" xml:space="preserve">
    <value>Previous job of applicant</value>
  </data>
  <data name="DateAndTime" xml:space="preserve">
    <value>Date and Time</value>
  </data>
  <data name="Exception_SapInvocePartialCancel" xml:space="preserve">
    <value>SAP partial refunds cannot be made for an uncollected application.</value>
  </data>
  <data name="IncorrectApplicationStatus" xml:space="preserve">
    <value>Fix Incorrect Application Status</value>
  </data>
  <data name="EntryDateCannotBeLessThanTheApplicationTime" xml:space="preserve">
    <value>Entry date cannot  be less than the application time</value>
  </data>
  <data name="UpdatedCancelledQuantity" xml:space="preserve">
    <value>Updated Cancelled Quantity</value>
  </data>
  <data name="UpdatedQuantity" xml:space="preserve">
    <value>Updated Quantity</value>
  </data>
  <data name="UpdateApplicationTime" xml:space="preserve">
    <value>Update Application Time</value>
  </data>
  <data name="InsuranceNotExist" xml:space="preserve">
    <value>Insurance not exist</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russian</value>
  </data>
  <data name="Turkmen" xml:space="preserve">
    <value>Turkmen</value>
  </data>
  <data name="IcrNoteRussian" xml:space="preserve">
    <value>Icr note (Russian)</value>
  </data>
  <data name="IcrNoteTurkmen" xml:space="preserve">
    <value>Icr note (Turkmen)</value>
  </data>
  <data name="LocalAuthorityStatus" xml:space="preserve">
    <value>Local Authority Status</value>
  </data>
  <data name="LocalAuthorityStatusIsRequired" xml:space="preserve">
    <value>Local Authority Status is required</value>
  </data>
  <data name="FirstVisaId" xml:space="preserve">
    <value>First VISA ID</value>
  </data>
  <data name="SecondVisaId" xml:space="preserve">
    <value>Updated VISA ID</value>
  </data>
  <data name="UpdatedInfo" xml:space="preserve">
    <value>Updated Information</value>
  </data>
  <data name="UpdatedLast" xml:space="preserve">
    <value>Last Updated</value>
  </data>
  <data name="AutomaticAccepted" xml:space="preserve">
    <value>Automatic Accepted</value>
  </data>
  <data name="AddToFamilyMembers" xml:space="preserve">
    <value>Add to family members</value>
  </data>
  <data name="CheckUnrealDocumentStatusPeriod" xml:space="preserve">
    <value>Check unreal document status period</value>
  </data>
  <data name="ExistingUnrealDocumentApplicationStatus" xml:space="preserve">
    <value>Existing unreal document application status</value>
  </data>
  <data name="PreviouslyHasUnrealDocument" xml:space="preserve">
    <value>Previously has unreal document</value>
  </data>
  <data name="CheckUnrealDocumentStatus" xml:space="preserve">
    <value>Check unreal document status</value>
  </data>
  <data name="RelatedProcessVisaId" xml:space="preserve">
    <value>VISA ID for the related transaction</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Turkish</value>
  </data>
  <data name="ExplicitConsentText" xml:space="preserve">
    <value>Explicit Consent Text</value>
  </data>
  <data name="OutscanToEmbassy" xml:space="preserve">
    <value>Outscan to embassy</value>
  </data>
  <data name="VisaFeeInformation" xml:space="preserve">
    <value>Visa Fee Information</value>
  </data>
  <data name="TurkmenistanConsulateReportTitle" xml:space="preserve">
    <value>DELIVERY OF VISA SUBJECT TO DATED FEE</value>
  </data>
  <data name="AddSurvey" xml:space="preserve">
    <value>Add Survey</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Survey" xml:space="preserve">
    <value>Survey</value>
  </data>
  <data name="UsingCalendarNotification" xml:space="preserve">
    <value>Please select a date from the calendar.</value>
  </data>
  <data name="VisaOfficerObservation" xml:space="preserve">
    <value>VISA OFFICER OBSERVATION</value>
  </data>
  <data name="Refund" xml:space="preserve">
    <value>Refund</value>
  </data>
  <data name="NetTotal" xml:space="preserve">
    <value>Net Total</value>
  </data>
  <data name="HimselfHerself" xml:space="preserve">
    <value>Himself/Herself</value>
  </data>
  <data name="ApplicationsPreviousNote" xml:space="preserve">
    <value>Applications previous note</value>
  </data>
  <data name="PreviousApplicationUnrealNll" xml:space="preserve">
    <value>Previous Application NLL(Unreal Document)</value>
  </data>
  <data name="SecondRefundIsDoneInsureCheck" xml:space="preserve">
    <value>Due to ongoing insurance, a second refund cannot be made</value>
  </data>
  <data name="ShowPreviousApplication" xml:space="preserve">
    <value>Show previous application</value>
  </data>
  <data name="CheckRejectionWithCountryEntryBannedStatus" xml:space="preserve">
    <value>Check YGY-R status</value>
  </data>
  <data name="CheckRejectionWithCountryEntryBannedStatusPeriod" xml:space="preserve">
    <value>Check YGY-R status period</value>
  </data>
  <data name="ExistingRejectionWithCountryEntryBannedStatus" xml:space="preserve">
    <value>Existing rejection with country entry banned (YGY-R) status</value>
  </data>
  <data name="PreviouslyHasRejectionWithCountryEntryBannedStatus" xml:space="preserve">
    <value>Previously has rejection with country entry banned (YGY-R) status</value>
  </data>
  <data name="IsShowInReport" xml:space="preserve">
    <value>Show in Report</value>
  </data>
  <data name="LineType" xml:space="preserve">
    <value>Line Type</value>
  </data>
  <data name="AppointmentNumberIsRequired" xml:space="preserve">
    <value>Appointment Number Is Required</value>
  </data>
  <data name="LineTypeShouldBeSelected" xml:space="preserve">
    <value>Line type must be selected</value>
  </data>
  <data name="LineNumber" xml:space="preserve">
    <value>Line number</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>Assign</value>
  </data>
  <data name="HoldOn" xml:space="preserve">
    <value>Hold on</value>
  </data>
  <data name="Individual" xml:space="preserve">
    <value>Individual</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Not found</value>
  </data>
  <data name="ApplicantList" xml:space="preserve">
    <value>Applicant list</value>
  </data>
  <data name="Assignee" xml:space="preserve">
    <value>Assignee</value>
  </data>
  <data name="Assigner" xml:space="preserve">
    <value>Assigner</value>
  </data>
  <data name="ChooseLineAndOrCounter" xml:space="preserve">
    <value>Choose line and/or counter </value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="SelectTheLineType" xml:space="preserve">
    <value>Select the line type</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>Record not found</value>
  </data>
  <data name="ExistingRecord" xml:space="preserve">
    <value>Existing record</value>
  </data>
  <data name="PleaseSelectAtLeastOneApplication" xml:space="preserve">
    <value>Please select at least one application</value>
  </data>
  <data name="TokenStatusUpdatedAsCancelled" xml:space="preserve">
    <value>Token status updated as cancelled</value>
  </data>
  <data name="TokenStatusUpdatedAsNotFound" xml:space="preserve">
    <value>Token status has been updated to incomplete</value>
  </data>
  <data name="TokenStatusUpdatedAsPostponed" xml:space="preserve">
    <value>Token status updated as postponed</value>
  </data>
  <data name="TokenStatusUpdatedAsAssigned" xml:space="preserve">
    <value>Token status updated as assigned</value>
  </data>
  <data name="TokenStatusUpdatedAsCompleted" xml:space="preserve">
    <value>Token status updated as completed</value>
  </data>
  <data name="TokenStatusUpdatedAsHoldOn" xml:space="preserve">
    <value>Token status updated as hold on</value>
  </data>
  <data name="LineNotFound" xml:space="preserve">
    <value>Line not found</value>
  </data>
  <data name="TokenNotFound" xml:space="preserve">
    <value>Token not found</value>
  </data>
  <data name="AppointmentNotFound" xml:space="preserve">
    <value>Appointment not found</value>
  </data>
  <data name="WrongBranchNotification" xml:space="preserve">
    <value>Wrong branch notification</value>
  </data>
  <data name="MoreThanOnePersonShouldBeSelectedForFamilyOrGroupAppointments" xml:space="preserve">
    <value>More than one person should be selected for family or group Appointments</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Resource is already registered</value>
  </data>
  <data name="TicketHaBeenTakenForSelectedPeople" xml:space="preserve">
    <value>Ticket has been taken for selected person/people</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="AreYouSureToAssignThisRecord" xml:space="preserve">
    <value>Are you sure to assign this record</value>
  </data>
  <data name="AreYouSureToCancelThisRecord" xml:space="preserve">
    <value>Are you sure to cancel this record ?</value>
  </data>
  <data name="AreYouSureToHoldOnThisRecord" xml:space="preserve">
    <value>Are you sure to hold on this record ?</value>
  </data>
  <data name="AreYouSureToNotFoundThisRecord" xml:space="preserve">
    <value>Are you sure you want to mark this application as incomplete?</value>
  </data>
  <data name="EnterNumericAppointmentNumber" xml:space="preserve">
    <value>Please enter a valid and numeric appointment number</value>
  </data>
  <data name="WhiteSpaceAppointmentNumber" xml:space="preserve">
    <value>Please enter appointment number</value>
  </data>
  <data name="ApplicantWithHasTicketNotFound" xml:space="preserve">
    <value>Applicant with has ticket not found</value>
  </data>
  <data name="ApplicantNotFound" xml:space="preserve">
    <value>Applicant not found</value>
  </data>
  <data name="SearchOperationNotValidForThisLineDepartment" xml:space="preserve">
    <value>Search operation not valid for this line department</value>
  </data>
  <data name="NumberOfApplications" xml:space="preserve">
    <value>Number of applications</value>
  </data>
  <data name="PleaseWaitForYourQueueNumberToBeCalled" xml:space="preserve">
    <value>Please wait for your queue number to be called. We thank you.</value>
  </data>
  <data name="BlockNumber" xml:space="preserve">
    <value>Block number</value>
  </data>
  <data name="CounterName" xml:space="preserve">
    <value>Counter name</value>
  </data>
  <data name="FloorNumber" xml:space="preserve">
    <value>Floor number</value>
  </data>
  <data name="LineDepartment" xml:space="preserve">
    <value>Line department</value>
  </data>
  <data name="AddCounter" xml:space="preserve">
    <value>Add counter</value>
  </data>
  <data name="CounterDetails" xml:space="preserve">
    <value>Counter details</value>
  </data>
  <data name="CounterList" xml:space="preserve">
    <value>Counter list</value>
  </data>
  <data name="UpdateCounter" xml:space="preserve">
    <value>Update counter</value>
  </data>
  <data name="AddToTheExistingFamilyOrGroupApplication" xml:space="preserve">
    <value>Add to the existing family or group application</value>
  </data>
  <data name="AppointmentIsCompleted" xml:space="preserve">
    <value>Appointment Is Completed</value>
  </data>
  <data name="TypeYourMessage" xml:space="preserve">
    <value>Type your message</value>
  </data>
  <data name="UpdateOrder" xml:space="preserve">
    <value>Update order</value>
  </data>
  <data name="SelectedLine" xml:space="preserve">
    <value>Selected line</value>
  </data>
  <data name="TokenRecalled" xml:space="preserve">
    <value>Token recalled</value>
  </data>
  <data name="AreYouSureToRecallThisRecord" xml:space="preserve">
    <value>Are you sure to recall this record</value>
  </data>
  <data name="CallNextFromHoldOn" xml:space="preserve">
    <value>Call next from hold on</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="CannotBeLeftBlank" xml:space="preserve">
    <value>This field cannot be left blank</value>
  </data>
  <data name="EnterValidQmsNote" xml:space="preserve">
    <value>Please enter a valid note</value>
  </data>
  <data name="CreateToken" xml:space="preserve">
    <value>Create Token</value>
  </data>
  <data name="ScanPassportAndAddNewApplicant" xml:space="preserve">
    <value>Scan passport and add new applicant</value>
  </data>
  <data name="LineDepartmentFlow" xml:space="preserve">
    <value>Line Department Flow</value>
  </data>
  <data name="AddLineDepartmentFlow" xml:space="preserve">
    <value>Add Line Department Flow</value>
  </data>
  <data name="LineList" xml:space="preserve">
    <value>Line List</value>
  </data>
  <data name="LineDepartmentFlowDetails" xml:space="preserve">
    <value>Line Department Flow Details</value>
  </data>
  <data name="UpdateLineDepartmentFlow" xml:space="preserve">
    <value>Update Line Department Flow</value>
  </data>
  <data name="Interval" xml:space="preserve">
    <value>Interval</value>
  </data>
  <data name="IsDepartmentCreateApplication" xml:space="preserve">
    <value>Is department create application</value>
  </data>
  <data name="ProcessSameCounter" xml:space="preserve">
    <value>Can make a group process</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Resource created</value>
  </data>
  <data name="ADepartmentShouldNotBeSelectedMoreThanOne" xml:space="preserve">
    <value>A department should not be selected more than one</value>
  </data>
  <data name="AtLeastOneDepartmentIsSelected" xml:space="preserve">
    <value>At least one department should be selected</value>
  </data>
  <data name="TheIntervalShouldNotBeSelectedZero" xml:space="preserve">
    <value>The interval should not be selected zero</value>
  </data>
  <data name="PleaseEnterLineName" xml:space="preserve">
    <value>Please enter line name</value>
  </data>
  <data name="Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Resource updated</value>
  </data>
  <data name="PleaseSelectTheDepartmentYouWantToAddInOrder" xml:space="preserve">
    <value>Please select the department you want to add in order</value>
  </data>
  <data name="Year_Month_Day" xml:space="preserve">
    <value>Year/Month/Day</value>
  </data>
  <data name="Second" xml:space="preserve">
    <value>sec</value>
  </data>
  <data name="VisaIsUsedYear" xml:space="preserve">
    <value>Visa Issued Year</value>
  </data>
  <data name="SelectedApplicantPostponed" xml:space="preserve">
    <value>Selected applicant postponed</value>
  </data>
  <data name="AreYouSureToChangeStatusOfThisApplicant" xml:space="preserve">
    <value>Are you sure to change status of this applicant ?</value>
  </data>
  <data name="TokenStatusUpdatedForSelectedApplicant" xml:space="preserve">
    <value>Token status updated for selected applicant</value>
  </data>
  <data name="IndividualAction" xml:space="preserve">
    <value>Individual action</value>
  </data>
  <data name="IndividualActionsAppliedToAllApplicants" xml:space="preserve">
    <value>Individual actions applied to all applicants</value>
  </data>
  <data name="SearchByPassportNumber" xml:space="preserve">
    <value>Search by passport number</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Scan" xml:space="preserve">
    <value>Scan</value>
  </data>
  <data name="DescriptionIsRequired" xml:space="preserve">
    <value>Description is required</value>
  </data>
  <data name="AreYouSureToRemoveAddedApplicant" xml:space="preserve">
    <value>Are you sure to remove added applicant ?</value>
  </data>
  <data name="PrintAll" xml:space="preserve">
    <value>Print All</value>
  </data>
  <data name="FinalStatusReport" xml:space="preserve">
    <value>Final Status Report</value>
  </data>
  <data name="RePrintTicket" xml:space="preserve">
    <value>Re Print Ticket</value>
  </data>
  <data name="QMS" xml:space="preserve">
    <value>QMS</value>
  </data>
  <data name="ATokenHasBeenCreatedForAllPeopleofThisAppointmentNumber" xml:space="preserve">
    <value>A token has been created for all people of this appointment number</value>
  </data>
  <data name="ActiveForEmail" xml:space="preserve">
    <value>Active for email</value>
  </data>
  <data name="ActiveForSms" xml:space="preserve">
    <value>Active for sms</value>
  </data>
  <data name="NotificationMail_Postpone" xml:space="preserve">
    <value>Your pre-appointment has been postponed to the [BRANCH] branch for the [DATE] date, [TIME] hour. Your appointment number is [APPNUMBER]</value>
  </data>
  <data name="MailFooter" xml:space="preserve">
    <value>This is an automatic mail. Please do not reply to this email.</value>
  </data>
  <data name="AddApplicationOfficialNote" xml:space="preserve">
    <value>Add Application Official Note</value>
  </data>
  <data name="ApplicationOfficialNote" xml:space="preserve">
    <value>Application Official Note</value>
  </data>
  <data name="ApplicationOfficialNotes" xml:space="preserve">
    <value>Application Official Notes</value>
  </data>
  <data name="OfficialNotes" xml:space="preserve">
    <value>Official Notes</value>
  </data>
  <data name="EmaaHasarExcelUpdate" xml:space="preserve">
    <value>Emaa Hasar Excel Update</value>
  </data>
  <data name="OpenToEmaaLossClaimEnty" xml:space="preserve">
    <value>Open Emaa loss file</value>
  </data>
  <data name="ApplicationDetailNotes" xml:space="preserve">
    <value>Application Detail Notes</value>
  </data>
  <data name="NetAmount" xml:space="preserve">
    <value>Net Amount</value>
  </data>
  <data name="NetQuantity" xml:space="preserve">
    <value>Net Quantity</value>
  </data>
  <data name="IsGroupInIcr" xml:space="preserve">
    <value>Is Group In Icr</value>
  </data>
  <data name="AddedToken" xml:space="preserve">
    <value>Added token</value>
  </data>
  <data name="RecallTimeLength" xml:space="preserve">
    <value>Recall time length</value>
  </data>
  <data name="EntireProcedureCompleteTimeLength" xml:space="preserve">
    <value>Entire procedure complete time length</value>
  </data>
  <data name="DepartmentNotDefined" xml:space="preserve">
    <value>Department is not defined for this branch</value>
  </data>
  <data name="TokenNumber" xml:space="preserve">
    <value>Token number</value>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>Action date</value>
  </data>
  <data name="PostponedDate" xml:space="preserve">
    <value>Postponed date</value>
  </data>
  <data name="AssignRatio" xml:space="preserve">
    <value>Assign ratio</value>
  </data>
  <data name="AverageServeTimeLength" xml:space="preserve">
    <value>Average serve time length</value>
  </data>
  <data name="CancelledRatio" xml:space="preserve">
    <value>Cancelled ratio</value>
  </data>
  <data name="CompletedRatio" xml:space="preserve">
    <value>Complete ratio</value>
  </data>
  <data name="HoldOnRatio" xml:space="preserve">
    <value>Hold on ratio</value>
  </data>
  <data name="NotFoundRatio" xml:space="preserve">
    <value>Not found ratio</value>
  </data>
  <data name="PostponeRatio" xml:space="preserve">
    <value>Postpone ratio</value>
  </data>
  <data name="TokenCalledCount" xml:space="preserve">
    <value>Token called count</value>
  </data>
  <data name="TotalServeTime" xml:space="preserve">
    <value>Total serve time</value>
  </data>
  <data name="Cabin" xml:space="preserve">
    <value>Cabin</value>
  </data>
  <data name="CabinName" xml:space="preserve">
    <value>Cabin name</value>
  </data>
  <data name="IsBiometricCabin" xml:space="preserve">
    <value>Is biometric cabin</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>Office</value>
  </data>
  <data name="OfficeName" xml:space="preserve">
    <value>Office name</value>
  </data>
  <data name="AddCabin" xml:space="preserve">
    <value>Add cabin</value>
  </data>
  <data name="AddOffice" xml:space="preserve">
    <value>Add Office</value>
  </data>
  <data name="CabinDetails" xml:space="preserve">
    <value>Cabin Details</value>
  </data>
  <data name="CabinList" xml:space="preserve">
    <value>Cabin List</value>
  </data>
  <data name="DeleteOffice" xml:space="preserve">
    <value>Delete Office</value>
  </data>
  <data name="OfficeCode" xml:space="preserve">
    <value>Office Code</value>
  </data>
  <data name="OfficeDetails" xml:space="preserve">
    <value>Office Details</value>
  </data>
  <data name="OfficeList" xml:space="preserve">
    <value>Office List</value>
  </data>
  <data name="UpdateCabin" xml:space="preserve">
    <value>Update cabin</value>
  </data>
  <data name="UpdateOffice" xml:space="preserve">
    <value>Update Office</value>
  </data>
  <data name="AddClientConfiguration" xml:space="preserve">
    <value>Add Client Configuration</value>
  </data>
  <data name="AddInventoryDefinition" xml:space="preserve">
    <value>Add Inventory Definition</value>
  </data>
  <data name="ClientConfigurationDetails" xml:space="preserve">
    <value>Client Configuration Details</value>
  </data>
  <data name="ClientConfigurationList" xml:space="preserve">
    <value>Client Configuration List</value>
  </data>
  <data name="HostName" xml:space="preserve">
    <value>Host name</value>
  </data>
  <data name="InventoryDefinition" xml:space="preserve">
    <value>Inventory Definition</value>
  </data>
  <data name="InventoryDefinitionDetails" xml:space="preserve">
    <value>Inventory Definition Details</value>
  </data>
  <data name="InventoryDefinitionList" xml:space="preserve">
    <value>Inventory Definition List</value>
  </data>
  <data name="UpdateClientConfiguration" xml:space="preserve">
    <value>Update ClientCon figuration</value>
  </data>
  <data name="UpdateInventoryDefinition" xml:space="preserve">
    <value>Update Inventory Definition</value>
  </data>
  <data name="AssignedInventories" xml:space="preserve">
    <value>Assigned Inventories</value>
  </data>
  <data name="AvailableInventories" xml:space="preserve">
    <value>Available Inventories</value>
  </data>
  <data name="UnAssign" xml:space="preserve">
    <value>Unassign</value>
  </data>
  <data name="ClaimNo" xml:space="preserve">
    <value>Loss Claim No</value>
  </data>
  <data name="RejectionRefundDoneLastDescription" xml:space="preserve">
    <value>the visa refusal has been refunded. Please inform the applicant.</value>
  </data>
  <data name="RejectionRefundDonePreviousDescription" xml:space="preserve">
    <value>for the previous application no</value>
  </data>
  <data name="RejectionRefundDonePermission" xml:space="preserve">
    <value>Number of Visa Refusal Checks</value>
  </data>
  <data name="AddBlackList" xml:space="preserve">
    <value>Add Black List</value>
  </data>
  <data name="AddWhiteList" xml:space="preserve">
    <value>Add White List</value>
  </data>
  <data name="BlackList" xml:space="preserve">
    <value>Black List</value>
  </data>
  <data name="WhiteList" xml:space="preserve">
    <value>White List</value>
  </data>
  <data name="IHBUploadedNotAvailableForThisApplication" xml:space="preserve">
    <value>IHB Uploaded not available for this application</value>
  </data>
  <data name="IHBDocumentNumber" xml:space="preserve">
    <value>IHB document number</value>
  </data>
  <data name="NotSuitable" xml:space="preserve">
    <value>Not suitable</value>
  </data>
  <data name="Suitable" xml:space="preserve">
    <value>Suitable</value>
  </data>
  <data name="IHBNumber" xml:space="preserve">
    <value>IHB number</value>
  </data>
  <data name="EvaluationDate" xml:space="preserve">
    <value>Evaluation date</value>
  </data>
  <data name="EvaluationTime" xml:space="preserve">
    <value>Evaluation time</value>
  </data>
  <data name="FileOperations" xml:space="preserve">
    <value>File operations</value>
  </data>
  <data name="FilterAllList" xml:space="preserve">
    <value>Filter all list</value>
  </data>
  <data name="FilterWaiting" xml:space="preserve">
    <value>Waiting</value>
  </data>
  <data name="IHBStatus" xml:space="preserve">
    <value>IHB status</value>
  </data>
  <data name="WalkinVasTypeControl" xml:space="preserve">
    <value>Walkin vas type control</value>
  </data>
  <data name="ContentType" xml:space="preserve">
    <value>Content Type</value>
  </data>
  <data name="Screen" xml:space="preserve">
    <value>Screen</value>
  </data>
  <data name="ScreenType" xml:space="preserve">
    <value>Screen Type</value>
  </data>
  <data name="Exception_NoWaitingForIhbApplicationFound" xml:space="preserve">
    <value>Application not found with waiting for IHB status</value>
  </data>
  <data name="ErrorNotFoundPolicyClaimLoss" xml:space="preserve">
    <value>The claim loss of the uninsured application has already been processed</value>
  </data>
  <data name="IsShowInRejectionList" xml:space="preserve">
    <value>Show in Reject List</value>
  </data>
  <data name="GeneratedTokenStatusChart" xml:space="preserve">
    <value>Generated token status chart</value>
  </data>
  <data name="NumberOfTokensByApplicantType" xml:space="preserve">
    <value>Number of tokens by applicant type</value>
  </data>
  <data name="NumberOfVisitorsDaily" xml:space="preserve">
    <value>Number of visitors daily</value>
  </data>
  <data name="TotalApplicantsUsedQmsByBranches" xml:space="preserve">
    <value>Total applicants used QMS by branches</value>
  </data>
  <data name="VisitorsByHourOfDay" xml:space="preserve">
    <value>Visitors by hour of day</value>
  </data>
  <data name="VisitorsByWeekday" xml:space="preserve">
    <value>Visitors by weekday</value>
  </data>
  <data name="HasPersonVisitedTurkeyBefore" xml:space="preserve">
    <value>Has person visited turkey before</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="HasVisitedTurkeyAndApprovedPercentage" xml:space="preserve">
    <value>Has visited Turkey and approved percentage</value>
  </data>
  <data name="HasVisitedTurkeyAndNonApprovedPercentage" xml:space="preserve">
    <value>Has visited Turkey and non approved percentage</value>
  </data>
  <data name="NegativeIHBPercentage" xml:space="preserve">
    <value>Negative IHB percentage</value>
  </data>
  <data name="NotApproved" xml:space="preserve">
    <value>Not approved</value>
  </data>
  <data name="PositiveIHBPercentage" xml:space="preserve">
    <value>Positive IHB percentage</value>
  </data>
  <data name="TurkmenistanApplicationStatistics" xml:space="preserve">
    <value>Turkmenistan application statistics</value>
  </data>
  <data name="LastHeartbeatTime" xml:space="preserve">
    <value>Last Heartbeat Time</value>
  </data>
  <data name="CourierInCity" xml:space="preserve">
    <value>Courier in City</value>
  </data>
  <data name="CourierOutOfCity" xml:space="preserve">
    <value>Courier Out of City</value>
  </data>
  <data name="DoubleTransitForForeigners" xml:space="preserve">
    <value>Double Transit for Foreigners</value>
  </data>
  <data name="DoubleTransitVisaFee" xml:space="preserve">
    <value>Double Transit Visa Fee</value>
  </data>
  <data name="FormFilling" xml:space="preserve">
    <value>Form Filling</value>
  </data>
  <data name="GatewayServiceFee" xml:space="preserve">
    <value>Gateway Service Fee</value>
  </data>
  <data name="GratisGWServiceFee" xml:space="preserve">
    <value>Gratis GW Service Fee</value>
  </data>
  <data name="GratisServiceFeeRequestedByEmbassy" xml:space="preserve">
    <value>Gratis Service Fee Requested by Embassy</value>
  </data>
  <data name="MultipleVisaFee" xml:space="preserve">
    <value>Multiple Visa Fee</value>
  </data>
  <data name="MultipleVisaFeeForForeigners" xml:space="preserve">
    <value>Multiple Visa Fee for Foreigners</value>
  </data>
  <data name="Photocopy" xml:space="preserve">
    <value>Photocopy</value>
  </data>
  <data name="Photograph" xml:space="preserve">
    <value>Photograph</value>
  </data>
  <data name="PrintOut" xml:space="preserve">
    <value>Print Out</value>
  </data>
  <data name="SingleEntryVisaFee" xml:space="preserve">
    <value>Single Entry Visa Fee</value>
  </data>
  <data name="SingleTransitForForeigners" xml:space="preserve">
    <value>Single Transit for Foreigners</value>
  </data>
  <data name="SingleTransitVisaFee" xml:space="preserve">
    <value>Single Transit Visa Fee</value>
  </data>
  <data name="SingleVisaFeeForForeigners" xml:space="preserve">
    <value>Single Visa Fee for Foreigners</value>
  </data>
  <data name="Staff" xml:space="preserve">
    <value>Staff</value>
  </data>
  <data name="VisaAssistService" xml:space="preserve">
    <value>Visa Assist Service</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="TokenReport" xml:space="preserve">
    <value>Token report</value>
  </data>
  <data name="LastActionCreatedAt" xml:space="preserve">
    <value>Last action at</value>
  </data>
  <data name="LastActionCreatedBy" xml:space="preserve">
    <value>Last action by</value>
  </data>
  <data name="TokenCreatedAt" xml:space="preserve">
    <value>Token time</value>
  </data>
  <data name="Called" xml:space="preserve">
    <value>Called</value>
  </data>
  <data name="TicketCreated" xml:space="preserve">
    <value>Ticket created</value>
  </data>
  <data name="TokenHistory" xml:space="preserve">
    <value>Token history</value>
  </data>
  <data name="FilterVip" xml:space="preserve">
    <value>Filter vip</value>
  </data>
  <data name="WaitingCount" xml:space="preserve">
    <value>Waiting count</value>
  </data>
  <data name="ActionReport" xml:space="preserve">
    <value>Action Report</value>
  </data>
  <data name="DepartmentReport" xml:space="preserve">
    <value>Department Report</value>
  </data>
  <data name="PersonalReport" xml:space="preserve">
    <value>Personal Report</value>
  </data>
  <data name="QmsReport" xml:space="preserve">
    <value>Qms Report</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="WhitelistProcessAuthorization" xml:space="preserve">
    <value>Whitelist process authorization</value>
  </data>
  <data name="ApplicationCreatedBy" xml:space="preserve">
    <value>Application created by</value>
  </data>
  <data name="IsWhiteListApplicant" xml:space="preserve">
    <value>Is whitelist applicant ?</value>
  </data>
  <data name="UnscannedPassport" xml:space="preserve">
    <value>Unscanned passport</value>
  </data>
  <data name="RepetitiveRecording" xml:space="preserve">
    <value>Repetitive recording</value>
  </data>
  <data name="OtherReason" xml:space="preserve">
    <value>Other Reason</value>
  </data>
  <data name="ErrorOpenedBeforeClaimLoss" xml:space="preserve">
    <value>Claim loss file of this application has already been opened</value>
  </data>
  <data name="AreYouSureToApproveThis" xml:space="preserve">
    <value>applications will be updated. Do you approve this ?</value>
  </data>
  <data name="StatusCorrectedByIncorrectApplication" xml:space="preserve">
    <value>Status Corrected by Incorrect Application</value>
  </data>
  <data name="AdditionalServiceType" xml:space="preserve">
    <value>Additional Service Type</value>
  </data>
  <data name="Exception_RefunedApplicationMust" xml:space="preserve">
    <value>For the application that has been collected , you must do this  a partial refund</value>
  </data>
  <data name="YSS_Check" xml:space="preserve">
    <value>YSS Check</value>
  </data>
  <data name="TypeOfInsurancePolicy" xml:space="preserve">
    <value>Type Of Insurance Policy</value>
  </data>
  <data name="Insured" xml:space="preserve">
    <value>Insured</value>
  </data>
  <data name="OnHold" xml:space="preserve">
    <value>On Hold</value>
  </data>
  <data name="EnryExitDateWarning" xml:space="preserve">
    <value>Incorrect date format. Please check the dates again.</value>
  </data>
  <data name="ExitDateWarning" xml:space="preserve">
    <value>The exit date is too far away from your entry date. Please check the dates again.</value>
  </data>
  <data name="TotalApplicantsTaken" xml:space="preserve">
    <value>Total applicants taken</value>
  </data>
  <data name="ActionsApplied" xml:space="preserve">
    <value>Actions Applied</value>
  </data>
  <data name="TotalHourlyServiceTime" xml:space="preserve">
    <value>Total Hourly Service Time</value>
  </data>
  <data name="AtLeastOneExtraFeeIsSelected" xml:space="preserve">
    <value>At Least One Extra Fee Is Selected</value>
  </data>
  <data name="ApplicationTakenFromPortal" xml:space="preserve">
    <value>Applications taken from Portal</value>
  </data>
  <data name="ApplicationTakenFromQms" xml:space="preserve">
    <value>Applications taken from QMS</value>
  </data>
  <data name="ServiceTimeMin" xml:space="preserve">
    <value>Service time (min)</value>
  </data>
  <data name="PleaseCheckTheExtraFees" xml:space="preserve">
    <value>Please Check The Extra Fees</value>
  </data>
  <data name="PolicyDate" xml:space="preserve">
    <value>Policy Date</value>
  </data>
  <data name="AreYouSureYouWantToUpdateThisInsurance" xml:space="preserve">
    <value>Are you Sure You Want To Update This Insurance?</value>
  </data>
  <data name="ApproveSms" xml:space="preserve">
    <value>Approve Sms</value>
  </data>
  <data name="RejectionSms" xml:space="preserve">
    <value>Rejection Sms</value>
  </data>
  <data name="AvailableForResend" xml:space="preserve">
    <value>Available for resend</value>
  </data>
  <data name="NotSent" xml:space="preserve">
    <value>Not Sent</value>
  </data>
  <data name="Resend" xml:space="preserve">
    <value>Resend</value>
  </data>
  <data name="SendDate" xml:space="preserve">
    <value>Send date</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>Sent</value>
  </data>
  <data name="SendStatus" xml:space="preserve">
    <value>Send status</value>
  </data>
  <data name="SMS" xml:space="preserve">
    <value>SMS</value>
  </data>
  <data name="SmsHistory" xml:space="preserve">
    <value>Sms history</value>
  </data>
  <data name="SmsType" xml:space="preserve">
    <value>Sms type</value>
  </data>
  <data name="NotYetDelivered" xml:space="preserve">
    <value>Not yet delivered</value>
  </data>
  <data name="Tokens" xml:space="preserve">
    <value>Tokens</value>
  </data>
  <data name="CreateInsuranceFirstWarning" xml:space="preserve">
    <value>Please Create Insurance First</value>
  </data>
  <data name="IsResended" xml:space="preserve">
    <value>Is Resended ?</value>
  </data>
  <data name="CCError" xml:space="preserve">
    <value>CC Error</value>
  </data>
  <data name="SaveAndSendAsMail" xml:space="preserve">
    <value>Save and send as mail</value>
  </data>
  <data name="SaveAndSendAsSMS" xml:space="preserve">
    <value>Save and send as SMS</value>
  </data>
  <data name="PreApplicationUpdatedBy" xml:space="preserve">
    <value>PreApplication Updated By</value>
  </data>
  <data name="MyCreations" xml:space="preserve">
    <value>My Creations</value>
  </data>
  <data name="PreApplicationCreatedBy" xml:space="preserve">
    <value>PreApplication Created By</value>
  </data>
  <data name="WorkPermitSms" xml:space="preserve">
    <value>Work permit sms</value>
  </data>
  <data name="IsAuthorizedForPostponeButton" xml:space="preserve">
    <value>Is Authorized For Postpone Button ?</value>
  </data>
  <data name="IsAuthorizedForQmsButton" xml:space="preserve">
    <value>Is Authorized For Qms Button ?</value>
  </data>
  <data name="NotCompleted" xml:space="preserve">
    <value>Not Completed</value>
  </data>
  <data name="InterviewDone" xml:space="preserve">
    <value>Interview is done</value>
  </data>
  <data name="InterviewRequired" xml:space="preserve">
    <value>Interview is required</value>
  </data>
  <data name="Exeption_EmailFormat" xml:space="preserve">
    <value>The email format is incorrect. Please fix!</value>
  </data>
  <data name="FamilyReunificationApplicant" xml:space="preserve">
    <value>Family Reunification Applicant</value>
  </data>
  <data name="NoApprovalRequired" xml:space="preserve">
    <value>No Approval Required</value>
  </data>
  <data name="NonTurkmenistanCitizen" xml:space="preserve">
    <value>Non-Turkmenistan Citizen</value>
  </data>
  <data name="NotRequiredForApproval" xml:space="preserve">
    <value>Not Required for Approval</value>
  </data>
  <data name="WaitingApproval" xml:space="preserve">
    <value>Waiting Approval</value>
  </data>
  <data name="PreApplicationExcelUpload" xml:space="preserve">
    <value>Appointment Excel Upload Area</value>
  </data>
  <data name="AddPreApplicationsToSystem" xml:space="preserve">
    <value>Add pre applications to system</value>
  </data>
  <data name="InvalidReason" xml:space="preserve">
    <value>Invalid reason</value>
  </data>
  <data name="RecordsInserted" xml:space="preserve">
    <value>Records inserted</value>
  </data>
  <data name="Valid" xml:space="preserve">
    <value>Valid</value>
  </data>
  <data name="HasSamePassportNumberInExcel" xml:space="preserve">
    <value>Has same passport number in excel</value>
  </data>
  <data name="RejectedApplicationsByPhone" xml:space="preserve">
    <value>Rejected Applications by Phone Number</value>
  </data>
  <data name="PreviousRejectedApplicationsFoundByPhone" xml:space="preserve">
    <value>Previous rejected applications found with these phone numbers</value>
  </data>
  <data name="PhoneRejectionCheckNote" xml:space="preserve">
    <value>Click Next again to continue or change the phone numbers</value>
  </data>
  <data name="NoRejectedApplicationsFound" xml:space="preserve">
    <value>No rejected applications found</value>
  </data>
  <data name="RejectionDate" xml:space="preserve">
    <value>Rejection Date</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="BiometricData" xml:space="preserve">
    <value>Biometric Data</value>
  </data>
  <data name="DocumentExemption" xml:space="preserve">
    <value>Document Exemption</value>
  </data>
  <data name="MissionNotes" xml:space="preserve">
    <value>Mission Notes</value>
  </data>
  <data name="RelevantInstitutionPerson" xml:space="preserve">
    <value>Relevant Institution or Person</value>
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value>Confirmation Code</value>
  </data>
  <data name="ApprovalStatus" xml:space="preserve">
    <value>Approval status</value>
  </data>
  <data name="SendConfirmationCode" xml:space="preserve">
    <value>Send confirmation code</value>
  </data>
  <data name="ApproveStatus" xml:space="preserve">
    <value>Approve status</value>
  </data>
  <data name="PleaseSelectConfirmationType" xml:space="preserve">
    <value>Please select confirmation type</value>
  </data>
  <data name="VoiceAnnouncement" xml:space="preserve">
    <value>Voice Announcement</value>
  </data>
  <data name="ViewWhiteListNotes" xml:space="preserve">
    <value>View white list notes</value>
  </data>
  <data name="WhiteListNotes" xml:space="preserve">
    <value>White list notes</value>
  </data>
  <data name="ConfirmationCodeSms" xml:space="preserve">
    <value>Confirmation code SMS</value>
  </data>
  <data name="InsuranceType" xml:space="preserve">
    <value>Insurance Type</value>
  </data>
  <data name="UpdateInsurance" xml:space="preserve">
    <value>Update Insurance</value>
  </data>
  <data name="UpdateInsuranceCheck" xml:space="preserve">
    <value>In this process, the first insurance policy of the person will be canceled and a new insurance policy will be created. Do you want to continue?</value>
  </data>
  <data name="SelectedAppointmentInAnotherBranch" xml:space="preserve">
    <value>Selected appointment in another branch</value>
  </data>
  <data name="FirstInsurancePolicyDuration" xml:space="preserve">
    <value>First Insurance Policy Duration</value>
  </data>
  <data name="FirstInsurancePolicyNumber" xml:space="preserve">
    <value>First Insurance Policy Number</value>
  </data>
  <data name="FirstInsurancePolicyPrice" xml:space="preserve">
    <value>First Insurance Policy Price</value>
  </data>
  <data name="RefundAmount" xml:space="preserve">
    <value>Refund Amount</value>
  </data>
  <data name="UpdateInsurancePolicyDuration" xml:space="preserve">
    <value>Update Insurance Policy Duration</value>
  </data>
  <data name="UpdateInsurancePolicyNumber" xml:space="preserve">
    <value>Update Insurance Policy Number</value>
  </data>
  <data name="UpdateInsurancePolicyPrice" xml:space="preserve">
    <value>Update Insurance Policy Price</value>
  </data>
  <data name="WhitelistReport" xml:space="preserve">
    <value>Whitelist Report</value>
  </data>
  <data name="IndiaEarlyApplicationAuthorization" xml:space="preserve">
    <value>Hindistan 1 Yıl Filtrelemesi</value>
  </data>
  <data name="NotificationContent" xml:space="preserve">
    <value>Notification content</value>
  </data>
  <data name="SmsConfirmationCode" xml:space="preserve">
    <value>SMS confirmation</value>
  </data>
  <data name="PleaseConsultYourManager" xml:space="preserve">
    <value>Please consult to your manager before creating a token! The entered passport number is in the expired Whitelist records</value>
  </data>
  <data name="FHI" xml:space="preserve">
    <value>FHI</value>
  </data>
  <data name="TI" xml:space="preserve">
    <value>TI</value>
  </data>
  <data name="MailConfirmationCode" xml:space="preserve">
    <value>Mail confirmation</value>
  </data>
  <data name="CreatedToday" xml:space="preserve">
    <value>Created Today</value>
  </data>
  <data name="ApplicationReceiver" xml:space="preserve">
    <value>Application Receiver</value>
  </data>
  <data name="PolicyPeriodAccordingToPassportExpireDate" xml:space="preserve">
    <value>Depending on the passport expiration date, 3 or 6-months insurance should be selected</value>
  </data>
  <data name="MultipleEntryVisaAccordingToPassportExpireDate" xml:space="preserve">
    <value>Cannot Apply for Multiple Entry Visa with Passport Expire Date of Less than 1 Year</value>
  </data>
  <data name="CargoBarcode" xml:space="preserve">
    <value>Print Cargo Barcode</value>
  </data>
  <data name="ChassisNumber" xml:space="preserve">
    <value>Chassis Number</value>
  </data>
  <data name="ModelYear" xml:space="preserve">
    <value>Model Year</value>
  </data>
  <data name="PlateNo" xml:space="preserve">
    <value>Plate No</value>
  </data>
  <data name="VehicleType" xml:space="preserve">
    <value>Vehicle Type</value>
  </data>
  <data name="UPS" xml:space="preserve">
    <value>UPS</value>
  </data>
  <data name="ActivateCargoIntegration" xml:space="preserve">
    <value>Activate Cargo Integration</value>
  </data>
  <data name="IsCargoIntegrationActive" xml:space="preserve">
    <value>Is Cargo Integration Active</value>
  </data>
  <data name="IcrNoteFrench" xml:space="preserve">
    <value>ICR note (French)</value>
  </data>
  <data name="CargoStatus" xml:space="preserve">
    <value>Cargo Status</value>
  </data>
  <data name="BarcodeCreatedBy" xml:space="preserve">
    <value>Barcode created by</value>
  </data>
  <data name="BarcodeCreatedDate" xml:space="preserve">
    <value>Barcode created at</value>
  </data>
  <data name="NotificationMail_NewPreApplication_Mail" xml:space="preserve">
    <value>Your pre-appointment has been completed to the [BRANCH] branch for the [DATE] date, [TIME] hour. Your appointment number is [APPNUMBER]</value>
  </data>
  <data name="AddressAreaInformation" xml:space="preserve">
    <value>Address area information</value>
  </data>
  <data name="UseCargoIntegration" xml:space="preserve">
    <value>Enter cargo information ?</value>
  </data>
  <data name="IsApplicationCreated" xml:space="preserve">
    <value>Is application created ?</value>
  </data>
  <data name="CameraId" xml:space="preserve">
    <value>Camera Id</value>
  </data>
  <data name="InvalidGuid" xml:space="preserve">
    <value>Invalid Guid</value>
  </data>
  <data name="IpCameras" xml:space="preserve">
    <value>Ip Cameras</value>
  </data>
  <data name="AddInventoryIpCamera" xml:space="preserve">
    <value>Add Ip Camera</value>
  </data>
  <data name="UpdateInventoryIpCamera" xml:space="preserve">
    <value>Update Ip Camera</value>
  </data>
  <data name="ThisCameraIdAllreadyExists" xml:space="preserve">
    <value>This Camera Id Allready Exists</value>
  </data>
  <data name="TrackingNumber" xml:space="preserve">
    <value>Tracking Number</value>
  </data>
  <data name="AddDetails" xml:space="preserve">
    <value>Add Details</value>
  </data>
  <data name="AddPrinter" xml:space="preserve">
    <value>Add Printer</value>
  </data>
  <data name="AddPrinterAgent" xml:space="preserve">
    <value>Add Printer Agent</value>
  </data>
  <data name="PrinterAgent" xml:space="preserve">
    <value>Printer Agent</value>
  </data>
  <data name="PrinterAgentDetail" xml:space="preserve">
    <value>Printer Agent Detail</value>
  </data>
  <data name="PrinterType" xml:space="preserve">
    <value>Printer Type</value>
  </data>
  <data name="RegistrationStatus" xml:space="preserve">
    <value>Registration Status</value>
  </data>
  <data name="Printer" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="CargoTrackingNumber" xml:space="preserve">
    <value>Cargo tracking number</value>
  </data>
  <data name="CargoReprintNotification" xml:space="preserve">
    <value>Update is available, do not forget to reprint the shipping address barcode</value>
  </data>
  <data name="PassportNo" xml:space="preserve">
    <value>Passport No</value>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>Phone No</value>
  </data>
  <data name="TrackingNo" xml:space="preserve">
    <value>Tracking No</value>
  </data>
  <data name="ActivatePrintAllIntegration" xml:space="preserve">
    <value>Activate Print-All Integration</value>
  </data>
  <data name="IsPrintAllIntegrationActive" xml:space="preserve">
    <value>Is Print-All Integration Active</value>
  </data>
  <data name="NotCompletedReason" xml:space="preserve">
    <value>Not completed reason</value>
  </data>
  <data name="MustBeSelectedForProcess" xml:space="preserve">
    <value>One of the options must be selected for the update process.</value>
  </data>
  <data name="IsPassportScanRequired" xml:space="preserve">
    <value>Pasaport scan required (QMS)</value>
  </data>
  <data name="QmsCompanyType" xml:space="preserve">
    <value>QMS company type</value>
  </data>
  <data name="SmsExtraFeeCheck" xml:space="preserve">
    <value>Sms extra fee check</value>
  </data>
  <data name="InventoryStatusPanel" xml:space="preserve">
    <value>Inventory Status Panel</value>
  </data>
  <data name="EVisa" xml:space="preserve">
    <value>E-Visa</value>
  </data>
  <data name="NumberOfDaysInStatus" xml:space="preserve">
    <value>Number of Days in Status</value>
  </data>
  <data name="EmailProvider" xml:space="preserve">
    <value>Email provider</value>
  </data>
  <data name="SmsProvider" xml:space="preserve">
    <value>Sms provider</value>
  </data>
  <data name="SavePrint" xml:space="preserve">
    <value>Save and Print</value>
  </data>
  <data name="InventoryStatusLogs" xml:space="preserve">
    <value>Inventory status logs</value>
  </data>
  <data name="NameOfSecondContactPerson" xml:space="preserve">
    <value>2. Name Of Contact Person</value>
  </data>
  <data name="CanNotReduceApplicantCount" xml:space="preserve">
    <value>All applications have been entered, cancel action must be taken to reduce the number of applicants.</value>
  </data>
  <data name="GovernorateInformation" xml:space="preserve">
    <value>Governorate information</value>
  </data>
  <data name="VersionTime" xml:space="preserve">
    <value>Version Time</value>
  </data>
  <data name="IsSendByPrefix" xml:space="preserve">
    <value>Send by operator based </value>
  </data>
  <data name="SmsSender" xml:space="preserve">
    <value>Sms sender</value>
  </data>
  <data name="ResidenceNumber" xml:space="preserve">
    <value>Residence Number</value>
  </data>
  <data name="AddLineDepartmentConnection" xml:space="preserve">
    <value>Add line departments connection</value>
  </data>
  <data name="ButtonName" xml:space="preserve">
    <value>Button name</value>
  </data>
  <data name="ConnectionList" xml:space="preserve">
    <value>Connection list</value>
  </data>
  <data name="LineDepartmentConnectionDetails" xml:space="preserve">
    <value>Line department connection detail</value>
  </data>
  <data name="LineDepartmentConnectionList" xml:space="preserve">
    <value>Line departments connection list</value>
  </data>
  <data name="ShowWaitingButton" xml:space="preserve">
    <value>Show waiting button</value>
  </data>
  <data name="UpdateLineDepartmentConnection" xml:space="preserve">
    <value>Update line department connection</value>
  </data>
  <data name="CopyCount" xml:space="preserve">
    <value>Copy Count</value>
  </data>
  <data name="ErrorOpenedBeforeInsurance" xml:space="preserve">
    <value>Visa rejection of an application without a policy number cannot be transferred to Sap</value>
  </data>
  <data name="ErrorOpenedBeforeSap" xml:space="preserve">
    <value>Visa rejection of applications without SAP registration will not be transferred to Sap</value>
  </data>
  <data name="DigitalSignature" xml:space="preserve">
    <value>Digital Signature</value>
  </data>
  <data name="SendToTablet" xml:space="preserve">
    <value>Send To Tablet</value>
  </data>
  <data name="Exception_ConvertingToHtml" xml:space="preserve">
    <value>Exception Converting To Html</value>
  </data>
  <data name="AppointmentDateChangeWarning" xml:space="preserve">
    <value>You have changed the date. You need to click on the "Search Slot" button.</value>
  </data>
  <data name="SecondRefundIsDoneOneYearLaterCheck1" xml:space="preserve">
    <value>Right to Visa Refund</value>
  </data>
  <data name="SecondRefundIsDoneOneYearLaterCheck2" xml:space="preserve">
    <value>on</value>
  </data>
  <data name="SecondRefundIsDoneOneYearLaterCheck3" xml:space="preserve">
    <value>in used</value>
  </data>
  <data name="VisaRejectionRefundApplicationNumberWarning" xml:space="preserve">
    <value>similar to Application Number and Visa Rejection Refund has been made.</value>
  </data>
  <data name="RejectFilesNotUploadedWarning" xml:space="preserve">
    <value>Reject Files are not uploaded, you can change the status after uploading the files.</value>
  </data>
  <data name="ActivatePrinterIntegration" xml:space="preserve">
    <value>Activate Printer Integration</value>
  </data>
  <data name="Tablet" xml:space="preserve">
    <value>Tablet</value>
  </data>
  <data name="PleaseSelectTablet" xml:space="preserve">
    <value>Please select a tablet</value>
  </data>
  <data name="RejectionRefundDoneCanNotBeSelectedForUD" xml:space="preserve">
    <value>You have selected NLL (Unreal Document) status. You cannot select Refund is Done status!</value>
  </data>
  <data name="RejectionRefundDoneCanNotBeSelectedForYGYR" xml:space="preserve">
    <value>You have selected YGY/R status. You cannot select Refund is Done status!</value>
  </data>
  <data name="PrintTsInsurancePolicy" xml:space="preserve">
    <value>Print Ts Insurance</value>
  </data>
  <data name="TsCreatePolicy" xml:space="preserve">
    <value>Ts Policy Create</value>
  </data>
  <data name="BrandModel" xml:space="preserve">
    <value>Brand Model</value>
  </data>
  <data name="WrongLanguageSelection" xml:space="preserve">
    <value>Wrong language selection</value>
  </data>
  <data name="CurrentPassword" xml:space="preserve">
    <value>Current Password</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="RetypeYourNewPassword" xml:space="preserve">
    <value>Retype Your New Password</value>
  </data>
  <data name="UpdatePasswordWarning" xml:space="preserve">
    <value>Your password has expired after 1 month. Please create a new password.</value>
  </data>
  <data name="UserPasswordUpdate" xml:space="preserve">
    <value>User Password Update</value>
  </data>
  <data name="UserPasswordRequirements" xml:space="preserve">
    <value>The password must be at least 8 characters, contain upper and lower case letters, numbers and special characters!</value>
  </data>
  <data name="NewPasswordNotMatch" xml:space="preserve">
    <value>New password does not match. Please re-enter the new password.</value>
  </data>
  <data name="PasswordCannotSame" xml:space="preserve">
    <value>Your new password cannot be the same as your last three passwords.</value>
  </data>
  <data name="PasswordIncorrectWarning" xml:space="preserve">
    <value>The current password is incorrect. Please ensure you have entered your current password correctly and try again.</value>
  </data>
  <data name="PleaseSelectAtLeastOne" xml:space="preserve">
    <value>Please select at least one</value>
  </data>
  <data name="AccountLockedWarning" xml:space="preserve">
    <value>Your account has been locked for 15 minutes after 3 incorrect login attempts. Please try again later.</value>
  </data>
  <data name="DeviceId" xml:space="preserve">
    <value>Device Id</value>
  </data>
  <data name="TabletList" xml:space="preserve">
    <value>Tablet List</value>
  </data>
  <data name="UpdateTablet" xml:space="preserve">
    <value>Update Tablet</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>Property Required</value>
  </data>
  <data name="ThereIsNoAppropriateDocumentation" xml:space="preserve">
    <value>There is no appropriate documentation</value>
  </data>
  <data name="DeviceIsOffline" xml:space="preserve">
    <value>Device is offline</value>
  </data>
  <data name="IsApplicationCreatedOnThisToken" xml:space="preserve">
    <value>Is application created on this token</value>
  </data>
  <data name="Student" xml:space="preserve">
    <value>Student</value>
  </data>
  <data name="Tomer" xml:space="preserve">
    <value>Tomer</value>
  </data>
  <data name="SendToEmbassyStatusDate" xml:space="preserve">
    <value>Scan Cycle Received at Embassy Status Date</value>
  </data>
  <data name="CheckRejectedDocuments" xml:space="preserve">
    <value>Check Rejected Documents</value>
  </data>
  <data name="UpdateWorkPermitPolicy" xml:space="preserve">
    <value>Update Work Permit Policy</value>
  </data>
  <data name="AreYouSureUpdateWorkPermitPolicy" xml:space="preserve">
    <value>In this process, the first insurance policy of the person will be canceled and a new insurance policy will be created. Do you want to continue?</value>
  </data>
  <data name="RejectionDocumentNotUploaded" xml:space="preserve">
    <value>Rejection Document Not Uploaded</value>
  </data>
  <data name="RejectionDocumentsIncompletelyUploaded" xml:space="preserve">
    <value>Rejection Documents Incompletely Uploaded</value>
  </data>
  <data name="LegalGuardian" xml:space="preserve">
    <value>Legal Guardian</value>
  </data>
  <data name="PleaseEnterLegalGuardianName" xml:space="preserve">
    <value>Please enter legal guardian name</value>
  </data>
  <data name="PleaseSelectTheApplicantParentType" xml:space="preserve">
    <value>Please select the applicant parent type</value>
  </data>
  <data name="LegalGuardianSignature" xml:space="preserve">
    <value>Legal Guardian Signature</value>
  </data>
  <data name="ApplicationIsUnder18" xml:space="preserve">
    <value>Application is under 18 years of age, select authorized signatory</value>
  </data>
  <data name="ProcessAt" xml:space="preserve">
    <value>Process at</value>
  </data>
  <data name="ViewNotCompletedReasons" xml:space="preserve">
    <value>View not completed reason</value>
  </data>
  <data name="ApplicationVisaRejection" xml:space="preserve">
    <value>Visa rejected applications</value>
  </data>
  <data name="ExcelExport" xml:space="preserve">
    <value>EXPORT TO EXCEL</value>
  </data>
  <data name="CourierSalesStatus" xml:space="preserve">
    <value>Courier sales status</value>
  </data>
  <data name="NumberOfApplicationsToCourierSales" xml:space="preserve">
    <value>Number of applications according to courier sales status</value>
  </data>
  <data name="TotalApplicationsForCourierSales" xml:space="preserve">
    <value>Total applications for courier sales</value>
  </data>
  <data name="TotalApplicationsWithoutCourierSales" xml:space="preserve">
    <value>Total applications without Courier Sales</value>
  </data>
  <data name="Approvement" xml:space="preserve">
    <value>Approvement</value>
  </data>
  <data name="ApprovementCodeManagement" xml:space="preserve">
    <value>Approvement code management</value>
  </data>
  <data name="ApprovementCodeMessage" xml:space="preserve">
    <value>You must enter a verification code for the refund to be made.</value>
  </data>
  <data name="EnterApprovementCode" xml:space="preserve">
    <value>Enter approvement code</value>
  </data>
  <data name="RefundStatus" xml:space="preserve">
    <value>Refund status</value>
  </data>
  <data name="Supervisor" xml:space="preserve">
    <value>Supervisor</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="Verify" xml:space="preserve">
    <value>Verify</value>
  </data>
  <data name="Exception_AlreadyVerified" xml:space="preserve">
    <value>This data already verified</value>
  </data>
  <data name="RejectionApprovalHistory" xml:space="preserve">
    <value>Rejection approval history</value>
  </data>
  <data name="CodeNotVerified" xml:space="preserve">
    <value>Code not verified</value>
  </data>
  <data name="CodeVerified" xml:space="preserve">
    <value>Code verified</value>
  </data>
  <data name="Exception_UserNotDefinedToThisBranchAsSupervisor" xml:space="preserve">
    <value>User not defined to this branch as a supervisor</value>
  </data>
  <data name="SupervisorVerification" xml:space="preserve">
    <value>Supervisor verification</value>
  </data>
  <data name="SupervisorVerificationMessage" xml:space="preserve">
    <value>Enter supervisor information for verification.</value>
  </data>
  <data name="CantSendNotificationMoreThanTwoTimes" xml:space="preserve">
    <value>Cant send rejection approval notification more than two times. Please select supervisor option</value>
  </data>
  <data name="BranchAuthorities" xml:space="preserve">
    <value>Branch authorities</value>
  </data>
  <data name="BranchAuthoritiesList" xml:space="preserve">
    <value>Branch authorities list</value>
  </data>
  <data name="BranchManager" xml:space="preserve">
    <value>Branch manager</value>
  </data>
  <data name="CountryManager" xml:space="preserve">
    <value>Country manager</value>
  </data>
  <data name="OperationManager" xml:space="preserve">
    <value>Operation manager</value>
  </data>
  <data name="ApprovementCompleteDate" xml:space="preserve">
    <value>Approvement complete date</value>
  </data>
  <data name="CodeResendCount" xml:space="preserve">
    <value>Code resend count</value>
  </data>
  <data name="CodeSendDate" xml:space="preserve">
    <value>Code send date</value>
  </data>
  <data name="RejectionApprovalSms" xml:space="preserve">
    <value>Rejection approval sms</value>
  </data>
  <data name="VerificationCompleted" xml:space="preserve">
    <value>Verification completed</value>
  </data>
  <data name="VerificationFailed" xml:space="preserve">
    <value>Verification failed</value>
  </data>
  <data name="ApproveEmail" xml:space="preserve">
    <value>Approve email</value>
  </data>
  <data name="EmailHistory" xml:space="preserve">
    <value>Email history</value>
  </data>
  <data name="EmailType" xml:space="preserve">
    <value>Email type</value>
  </data>
  <data name="RejectionApprovalEmail" xml:space="preserve">
    <value>Rejection approval email</value>
  </data>
  <data name="RejectionEmail" xml:space="preserve">
    <value>Rejection email</value>
  </data>
  <data name="ConfirmationCodeEmail" xml:space="preserve">
    <value>Confirmation code mail</value>
  </data>
  <data name="WorkPermitEmail" xml:space="preserve">
    <value>Work permit mail</value>
  </data>
  <data name="PhotoBoothList" xml:space="preserve">
    <value>PhotoBooth List</value>
  </data>
  <data name="BadShotPhotoBooth" xml:space="preserve">
    <value>Bad Shot</value>
  </data>
  <data name="DeletedPhotoBooth" xml:space="preserve">
    <value>Deleted PhotoBooth</value>
  </data>
  <data name="ExpiredPhotoBooth" xml:space="preserve">
    <value>Expired PhotoBoths</value>
  </data>
  <data name="ThreeUpdatedBarcodeNumber" xml:space="preserve">
    <value>Three Updated Barcode Number</value>
  </data>
  <data name="TwoUpdatedBarcodeNumber" xml:space="preserve">
    <value>Two Updated Barcode Number</value>
  </data>
  <data name="CheckUsedPhotoBoothDeletedApplicationSure" xml:space="preserve">
    <value>There is a used photo booth barcode, are you sure you want to delete it?</value>
  </data>
  <data name="SmsCargoFeeCheck" xml:space="preserve">
    <value>Sms cargo fee check</value>
  </data>
  <data name="BranchNames" xml:space="preserve">
    <value>Branch Names</value>
  </data>
  <data name="UsedPhotoBooth" xml:space="preserve">
    <value>Used PhotoBooth</value>
  </data>
  <data name="OnlyOneExtraFeePhotoCanBeSelected" xml:space="preserve">
    <value>You can only choose 1 photo fee!</value>
  </data>
  <data name="ServiceError" xml:space="preserve">
    <value>Service Error</value>
  </data>
  <data name="EnterNotCompletedReason" xml:space="preserve">
    <value>Enter not completed reason</value>
  </data>
  <data name="QmsAllBranchesWhiteListReport" xml:space="preserve">
    <value>All Branches WhiteList Report</value>
  </data>
  <data name="QmsNoShowReport" xml:space="preserve">
    <value>No Show Report</value>
  </data>
  <data name="QmsVIPReport" xml:space="preserve">
    <value>VIP Report</value>
  </data>
  <data name="SearchAppointment" xml:space="preserve">
    <value>Search appointment</value>
  </data>
  <data name="PreApplicationHistory" xml:space="preserve">
    <value>Pre application history</value>
  </data>
  <data name="EnterNationalityPlease" xml:space="preserve">
    <value>Please enter nationality information</value>
  </data>
  <data name="EnterPassportNumberPlease" xml:space="preserve">
    <value>Please enter passport number</value>
  </data>
  <data name="ApplicationPreApplicationConnection" xml:space="preserve">
    <value>Application-preApplication connection</value>
  </data>
  <data name="TokenNumberProcess" xml:space="preserve">
    <value>Token Number Process</value>
  </data>
  <data name="EndwithCannotBeSmallerThanStartwith" xml:space="preserve">
    <value>Endwith cannot be smaller than startwith</value>
  </data>
  <data name="NoShow" xml:space="preserve">
    <value>No Show</value>
  </data>
  <data name="ThereIsAnAppointmentForThisPersonAtLaterDate" xml:space="preserve">
    <value>There is an appointment for this person at a later date</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="IsVerified" xml:space="preserve">
    <value>Is Verified</value>
  </data>
  <data name="VerfyMessageTemplate" xml:space="preserve">
    <value>The verification code you need to continue processing is:</value>
  </data>
  <data name="VerificationHistoryPageLabel" xml:space="preserve">
    <value>Contact information verification history via SMS E-Mail or Supervisor</value>
  </data>
  <data name="VerificationTime" xml:space="preserve">
    <value>Verification Time</value>
  </data>
  <data name="VerificationType" xml:space="preserve">
    <value>Verification Type</value>
  </data>
  <data name="VerifiedContactInformationTabLabel" xml:space="preserve">
    <value>Verified contact information</value>
  </data>
  <data name="VeriyfModalTitle" xml:space="preserve">
    <value>Verification Code Management</value>
  </data>
  <data name="ContactInformationVerificationMailSubject" xml:space="preserve">
    <value>Contac Information Verification</value>
  </data>
  <data name="QmsNotCompletedReport" xml:space="preserve">
    <value>Not completed report</value>
  </data>
  <data name="DisableContactInformationVerification" xml:space="preserve">
    <value>Disable Contact Information Verification</value>
  </data>
  <data name="BiometricsProcessingTime" xml:space="preserve">
    <value>Biometrics Processing Time</value>
  </data>
  <data name="BiometricsStaff" xml:space="preserve">
    <value>Biometrics Staff</value>
  </data>
  <data name="BiometricsWaitingTime" xml:space="preserve">
    <value>Biometrics Waiting Time</value>
  </data>
  <data name="QmsProcessTimeReport" xml:space="preserve">
    <value>Process Time Report</value>
  </data>
  <data name="SubmissionProcessingTime" xml:space="preserve">
    <value>Submission Processing Time</value>
  </data>
  <data name="SubmissionStaff" xml:space="preserve">
    <value>Submission Staff</value>
  </data>
  <data name="SubmissionWaitingTime" xml:space="preserve">
    <value>Submission Waiting Time</value>
  </data>
  <data name="TotalTime" xml:space="preserve">
    <value>Total Time</value>
  </data>
  <data name="LineChanging" xml:space="preserve">
    <value>Line changing</value>
  </data>
  <data name="TheLineTypeOfTheCurrentAppointmentWillBeChangedToVip" xml:space="preserve">
    <value>The line type of the current appointment will be changed to VIP</value>
  </data>
  <data name="VasTypeMustBeVip" xml:space="preserve">
    <value>Vas type must be vip</value>
  </data>
  <data name="FutureAppointment" xml:space="preserve">
    <value>Future appointment</value>
  </data>
  <data name="ThereIsFutureAppointmentForThisPassportNumber" xml:space="preserve">
    <value>There is a future appointment for this passport number. Registration is made through the VIP line</value>
  </data>
  <data name="EmailOrPhoneNumberMissing" xml:space="preserve">
    <value>Email or phone number missing</value>
  </data>
  <data name="ScanCycleRejectionApprovalControl" xml:space="preserve">
    <value>Rejection approval refund control</value>
  </data>
  <data name="RejectionApprovalWarning" xml:space="preserve">
    <value>The rejection approval process has not been completed. Please complete the rejection approval process first</value>
  </data>
  <data name="PhotoBoothIntegrationActive" xml:space="preserve">
    <value>Activate the Photo ICR Integration</value>
  </data>
  <data name="ContactInformationValidationFailed" xml:space="preserve">
    <value>Verification code is incorrect or expired</value>
  </data>
  <data name="SendNotificationCountMessage" xml:space="preserve">
    <value>Verification notification can be sent maximum 2 times. Please continue with Supervisor authority.</value>
  </data>
  <data name="NotAuthorizedBranchManager" xml:space="preserve">
    <value>Branch manager is not authorized in this branch.</value>
  </data>
  <data name="AppointmentBranch" xml:space="preserve">
    <value>Appointment Branch</value>
  </data>
  <data name="ApplicationUpdateStatusCheckActive" xml:space="preserve">
    <value>Application update status check (rejection approval)</value>
  </data>
  <data name="IsRestrictChangeContactInformation" xml:space="preserve">
    <value>Restrict change contact information</value>
  </data>
  <data name="MissionRejectionWarning" xml:space="preserve">
    <value>According to the mission decision, this applicant cannot apply!</value>
  </data>
  <data name="DatePickerFirstMonthFormatView" xml:space="preserve">
    <value>MM/dd/yyyy</value>
  </data>
  <data name="IsEsimBeSent" xml:space="preserve">
    <value>Send E-Sim</value>
  </data>
  <data name="QmsTatReport" xml:space="preserve">
    <value>TAT Report</value>
  </data>
  <data name="TatService" xml:space="preserve">
    <value>Tat Service</value>
  </data>
  <data name="TokenProcessedBy" xml:space="preserve">
    <value>Processed by</value>
  </data>
  <data name="ShowInAllApplicationsReport" xml:space="preserve">
    <value>Show in All Applications Report</value>
  </data>
  <data name="ResidingCountry" xml:space="preserve">
    <value>Residing Country</value>
  </data>
  <data name="ExtraFeeSalesCannotBeMade" xml:space="preserve">
    <value>extra fee sales cannot be made at the moment!</value>
  </data>
  <data name="ExtraFeeCannotBeCancelled" xml:space="preserve">
    <value>Extra Fee cannot be canceled</value>
  </data>
  <data name="PrintEsimQr" xml:space="preserve">
    <value>Print E-Sim Qr</value>
  </data>
  <data name="DoubleTransitForForeignersDZD" xml:space="preserve">
    <value>Double Transit for Foreigners DZD</value>
  </data>
  <data name="MultipleVisaFeeForForeignersDZD" xml:space="preserve">
    <value>Multiple Visa Fee for Foreigners DZD</value>
  </data>
  <data name="PhotoDZD" xml:space="preserve">
    <value>Photo (DZD)</value>
  </data>
  <data name="SingleEntryVisaFeeForForeignersDZD" xml:space="preserve">
    <value>Single Entry Visa Fee for Foreigners DZD</value>
  </data>
  <data name="SingleTransitForForeignersDZD" xml:space="preserve">
    <value>Single Transit For Foreigners DZD</value>
  </data>
  <data name="ScanQrCodeWithinTurkiye" xml:space="preserve">
    <value>Please scan the QR Code while within the borders of Türkiye</value>
  </data>
  <data name="ApplicationSale" xml:space="preserve">
    <value>Application Sale</value>
  </data>
  <data name="FiveYearsExtraFee" xml:space="preserve">
    <value>5 Years Extra Fee</value>
  </data>
  <data name="FourYearsExtraFee" xml:space="preserve">
    <value>4 Years Extra Fee</value>
  </data>
  <data name="ThreeYearsExtraFee" xml:space="preserve">
    <value>3 Years Extra Fee</value>
  </data>
  <data name="TwoYearsExtraFee" xml:space="preserve">
    <value>2 Years Extra Fee</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="Reserved" xml:space="preserve">
    <value>Reserved</value>
  </data>
  <data name="Used" xml:space="preserve">
    <value>Used</value>
  </data>
  <data name="QmsDashboardFilterAccessedBranchOnly" xml:space="preserve">
    <value>Qms dashboard filter accessed branch only</value>
  </data>
  <data name="QmsCompletedReport" xml:space="preserve">
    <value>Completed Report</value>
  </data>
  <data name="SendKsaIcrGenereteEInvoice" xml:space="preserve">
    <value>Generate Tax E-Invoice</value>
  </data>
  <data name="PrintComplementaryServices" xml:space="preserve">
    <value>Print Complementary Services</value>
  </data>
  <data name="isAuthorizedForDeletePhotoBooth" xml:space="preserve">
    <value>Is Authorized For Delete Photobooth</value>
  </data>
  <data name="OnlyOneExtraFeeESIMCanBeSelected" xml:space="preserve">
    <value>You can only choose 1 eSIM fee!</value>
  </data>
  <data name="Photo" xml:space="preserve">
    <value>Photo</value>
  </data>
  <data name="ShowCityDropdown" xml:space="preserve">
    <value>Show city dropdown</value>
  </data>
  <data name="CostMode" xml:space="preserve">
    <value>Cost mode</value>
  </data>
  <data name="InCity" xml:space="preserve">
    <value>In city</value>
  </data>
  <data name="OutOfCity" xml:space="preserve">
    <value>Out city</value>
  </data>
  <data name="QmsAppointmentPassTime" xml:space="preserve">
    <value>The appointment time for appointment number has passed.</value>
  </data>
  <data name="RejectionRefundDoneCanNotBeSelected" xml:space="preserve">
    <value>You cannot select Refund is Done status!</value>
  </data>
  <data name="VisaFeeAmount" xml:space="preserve">
    <value>Visa Fee Amount</value>
  </data>
  <data name="IsSlotTypesConnected" xml:space="preserve">
    <value>Is slots connected ?</value>
  </data>
  <data name="DeleteReason" xml:space="preserve">
    <value>Delete Reason</value>
  </data>
  <data name="B2CAppointment" xml:space="preserve">
    <value>B2C Appointment</value>
  </data>
  <data name="ReceivedAtEmbassy" xml:space="preserve">
    <value>Received at embassy</value>
  </data>
  <data name="SentToVACFromEmbassy" xml:space="preserve">
    <value>Sent to VAC from embassy</value>
  </data>
  <data name="ConsularReporting" xml:space="preserve">
    <value>Consular Reports</value>
  </data>
  <data name="ApplicationListUpdatedAt" xml:space="preserve">
    <value>Application list updated at</value>
  </data>
  <data name="ApplicationListUpdatedBy" xml:space="preserve">
    <value>Application list updated by</value>
  </data>
  <data name="ApplicationListUpdatedInformation" xml:space="preserve">
    <value>Application list updated information</value>
  </data>
  <data name="ApplicationNotReceived" xml:space="preserve">
    <value>Application not received</value>
  </data>
  <data name="ApplicationReceived" xml:space="preserve">
    <value>Application received</value>
  </data>
  <data name="TypeOfVisa" xml:space="preserve">
    <value>Type of visa</value>
  </data>
  <data name="LdapAccountLockedWarning" xml:space="preserve">
    <value>Your account has been locked. Please contact the system administrator.</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Answer</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="PleaseAnswerRequiredQuestions" xml:space="preserve">
    <value>Please answer required questions</value>
  </data>
  <data name="UpdateSurvey" xml:space="preserve">
    <value>Update survey</value>
  </data>
  <data name="AgentObservation" xml:space="preserve">
    <value>Application Staff Observations</value>
  </data>
  <data name="SurveySummary" xml:space="preserve">
    <value>Survey summary</value>
  </data>
  <data name="ResidenceApplication" xml:space="preserve">
    <value>RP Application</value>
  </data>
  <data name="ResidenceApplicationToBeMade" xml:space="preserve">
    <value>Residence Application To Be Made</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="ExtraPackagesApplication" xml:space="preserve">
    <value>Extra Packages Application</value>
  </data>
  <data name="ExtraPackagesApplicationFee" xml:space="preserve">
    <value>Extra Packages Application Fee</value>
  </data>
  <data name="Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="TurquoiseGratis" xml:space="preserve">
    <value>Turquoise Gratis</value>
  </data>
  <data name="Unspecified" xml:space="preserve">
    <value>Unspecified</value>
  </data>
  <data name="ApplicationExcelUpload" xml:space="preserve">
    <value>Application excel upload</value>
  </data>
  <data name="ShowInB2c" xml:space="preserve">
    <value>Show in B2C</value>
  </data>
  <data name="VisaExemptionIraqUnder15" xml:space="preserve">
    <value>Iraq citizens under the age of 15 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionIraqOver50" xml:space="preserve">
    <value>Iraq citizens over the age of 50 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionAlgerianUnder15" xml:space="preserve">
    <value>Algerian citizens under the age of 15 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionAlgerianOver65" xml:space="preserve">
    <value>Algerian citizens over the age of 65 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionLibyanUnder16" xml:space="preserve">
    <value>Libyan citizens under the age of 16 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionLibyanOver45" xml:space="preserve">
    <value>Libyan citizens over the age of 45 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionEgyptianUnder20" xml:space="preserve">
    <value>Egyptian citizens under the age of 20 are exempt from visas!</value>
  </data>
  <data name="VisaExemptionEgyptianOver45" xml:space="preserve">
    <value>Egyptian citizens over the age of 45 are exempt from visas!</value>
  </data>
  <data name="VisaExemption" xml:space="preserve">
    <value>Visa Exemption</value>
  </data>
  <data name="VisaExemptReportTitle" xml:space="preserve">
    <value>Visa Exempt Application by Age Range</value>
  </data>
  <data name="Approval" xml:space="preserve">
    <value>Approval</value>
  </data>
  <data name="Istizan" xml:space="preserve">
    <value>Istizan</value>
  </data>
  <data name="IstizanApproval" xml:space="preserve">
    <value>Istizan Approval</value>
  </data>
  <data name="IstizanRejection" xml:space="preserve">
    <value>Istizan Rejection</value>
  </data>
  <data name="NumberOfPassportsReceivedAtEmbassy" xml:space="preserve">
    <value>Number of Passports Received at Embassy</value>
  </data>
  <data name="RejectedNotIncludingIR" xml:space="preserve">
    <value>Rejected (Not Including IR)</value>
  </data>
  <data name="ApplicationTogetherRequirementWarning" xml:space="preserve">
    <value>Fill in the number of applications together</value>
  </data>
  <data name="Zero_Fifteen" xml:space="preserve">
    <value>0-15 years old</value>
  </data>
  <data name="MoreThanFifty" xml:space="preserve">
    <value>50 years and more</value>
  </data>
  <data name="ByAgeRangeWithApplicationTogether" xml:space="preserve">
    <value>By Age Range With Application Together</value>
  </data>
  <data name="IsAuthorizedForTurkmenistanStatisticReport" xml:space="preserve">
    <value>Is Authorized For Turkmenistan Statistic Report ?</value>
  </data>
  <data name="QmsVfsAppointmentsEntrySelectFilesButtonLabelName" xml:space="preserve">
    <value>Select Files</value>
  </data>
  <data name="QmsVfsAppointmentsEntryClearButtonLabelName" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="QmsVfsAppointmentsEntryUploadButtonLabelName" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="QmsVfsAppointmentsUploadSuccessMessage" xml:space="preserve">
    <value>File(s) uploaded succesfully.</value>
  </data>
  <data name="QmsVfsAppointmentsEntryDropFilesHereInfo" xml:space="preserve">
    <value>Drop files here to upload</value>
  </data>
  <data name="QmsVfsAppointmentsEntryStatusUploaded" xml:space="preserve">
    <value>Done</value>
  </data>
  <data name="QmsDashboardResetButtonLabelName" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="QmsDashboardApplyFiltersButtonLabelName" xml:space="preserve">
    <value>Apply Filters</value>
  </data>
  <data name="WhitelistResenApplicationReport" xml:space="preserve">
    <value>Whitelist Resen Application Report</value>
  </data>
  <data name="WhiteListResen" xml:space="preserve">
    <value>White list resen</value>
  </data>
  <data name="AddWhiteListResen" xml:space="preserve">
    <value>Add Whitelist Resen Application</value>
  </data>
  <data name="UpdateWhiteListResen" xml:space="preserve">
    <value>Update WhiteList Resen Application</value>
  </data>
  <data name="PortalUserNotFound" xml:space="preserve">
    <value>User not found. Please contact the system administrator</value>
  </data>
  <data name="PortalUserRoleNotFound" xml:space="preserve">
    <value>The user does not have permission. Please contact the Software Support Team</value>
  </data>
  <data name="SendBasicGuidelineInAms" xml:space="preserve">
    <value>Send basic guideline (AMS)</value>
  </data>
  <data name="BasicGuidelineType" xml:space="preserve">
    <value>Basic guidline type</value>
  </data>
  <data name="QmsAllReport" xml:space="preserve">
    <value>All reports (QMS)</value>
  </data>
  <data name="OneDay" xml:space="preserve">
    <value>1 day</value>
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value>1 week</value>
  </data>
  <data name="SelectFilterMessage" xml:space="preserve">
    <value>You need to select a date range</value>
  </data>
  <data name="ShowDigitalSignature" xml:space="preserve">
    <value>Show in Digital Signature</value>
  </data>
  <data name="AreYouSureToAddSlotAgain" xml:space="preserve">
    <value>Are you sure you want to add a slot again between {Date} {Branch}</value>
  </data>
  <data name="AreYouSureToDeleteSlot" xml:space="preserve">
    <value>Are you sure you want to delete slots in the {Branch}</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="StatusAmount" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="ApplicationReportOfRejectedPassports" xml:space="preserve">
    <value>Application Report Of Rejected Passports</value>
  </data>
  <data name="Ikibinonalti" xml:space="preserve">
    <value>2016</value>
  </data>
  <data name="Ikibinondokuz" xml:space="preserve">
    <value>2019</value>
  </data>
  <data name="Ikibinonsekiz" xml:space="preserve">
    <value>2018</value>
  </data>
  <data name="Ikibinonyedi" xml:space="preserve">
    <value>2017</value>
  </data>
  <data name="Ikibinyirmi" xml:space="preserve">
    <value>2020</value>
  </data>
  <data name="Ikibinyirmibir" xml:space="preserve">
    <value>2021</value>
  </data>
  <data name="Ikibinyirmiiki" xml:space="preserve">
    <value>2022</value>
  </data>
  <data name="Ikibinyirmiuc" xml:space="preserve">
    <value>2023</value>
  </data>
  <data name="Mission" xml:space="preserve">
    <value>Mission</value>
  </data>
  <data name="DigitalSignatureDocuments" xml:space="preserve">
    <value>Digital Signature Documents</value>
  </data>
  <data name="FileTypeName" xml:space="preserve">
    <value>File Type Name</value>
  </data>
  <data name="UsesDigitalSignature" xml:space="preserve">
    <value>Uses Digital Signature</value>
  </data>
  <data name="SelectedLanguages" xml:space="preserve">
    <value>Selected Languages</value>
  </data>
  <data name="RelatedInsuranceForExempt" xml:space="preserve">
    <value>Related Insurance for Exempt</value>
  </data>
  <data name="AppointmentPeriod" xml:space="preserve">
    <value>Appointment Period</value>
  </data>
  <data name="BranchShiftHoliday" xml:space="preserve">
    <value>Branch Shift/Holiday</value>
  </data>
  <data name="BranchShiftHolidayList" xml:space="preserve">
    <value>Branch Shift/Holiday List</value>
  </data>
  <data name="EndOfLunch" xml:space="preserve">
    <value>End Of Lunch</value>
  </data>
  <data name="EndOfShift" xml:space="preserve">
    <value>End Of Shift</value>
  </data>
  <data name="SelectBranchApplicationCountryDetail" xml:space="preserve">
    <value>Branch Application Country Detail</value>
  </data>
  <data name="SelectWeekWekeends" xml:space="preserve">
    <value>Week Wekeends</value>
  </data>
  <data name="StartOfLunch" xml:space="preserve">
    <value>Start Of Lunch</value>
  </data>
  <data name="StartOfShift" xml:space="preserve">
    <value>Start Of Shift </value>
  </data>
  <data name="BranchShift" xml:space="preserve">
    <value>Branch Shift</value>
  </data>
  <data name="BranchWeekends" xml:space="preserve">
    <value>Branch Weekends</value>
  </data>
  <data name="HolidayDay" xml:space="preserve">
    <value>Holiday Day</value>
  </data>
  <data name="ShowPaymentMethods" xml:space="preserve">
    <value>Show Payment Methods</value>
  </data>
  <data name="Cash" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="PaymentMethodsWarning" xml:space="preserve">
    <value>Extra Packages have an unselected Payment option.</value>
  </data>
  <data name="BranchHolidays" xml:space="preserve">
    <value>Branch Holidays</value>
  </data>
  <data name="HolidayDays" xml:space="preserve">
    <value>Holiday Days</value>
  </data>
  <data name="TotalCashCollections" xml:space="preserve">
    <value>Total Cash Collections</value>
  </data>
  <data name="TotalOnlineCollections" xml:space="preserve">
    <value>Total Online Collections</value>
  </data>
  <data name="TotalPaymentOptions" xml:space="preserve">
    <value>Total Payment Options</value>
  </data>
  <data name="TotalPosCollections" xml:space="preserve">
    <value>Total Pos Collections</value>
  </data>
  <data name="AddVisaType" xml:space="preserve">
    <value>Add Visa Type</value>
  </data>
  <data name="UpdateVisaType" xml:space="preserve">
    <value>Update Visa Type</value>
  </data>
  <data name="VisaTypeDetails" xml:space="preserve">
    <value>Visa Type Details</value>
  </data>
  <data name="VisaTypeName" xml:space="preserve">
    <value>Visa Type Name</value>
  </data>
  <data name="VisaTypes" xml:space="preserve">
    <value>Visa Types</value>
  </data>
  <data name="VisaType" xml:space="preserve">
    <value>Visa Type</value>
  </data>
  <data name="AddUpdateBranchApplicationCountryCompanyExtraFee" xml:space="preserve">
    <value>Add/Update Provider Company</value>
  </data>
  <data name="CompanyExtraFeePrice" xml:space="preserve">
    <value>Company Fee</value>
  </data>
  <data name="ProviderCompany" xml:space="preserve">
    <value>Provider Company</value>
  </data>
  <data name="MainExtraFeeCategory" xml:space="preserve">
    <value>Main Fee</value>
  </data>
  <data name="AddInquiry" xml:space="preserve">
    <value>Add inquiry</value>
  </data>
  <data name="Inquiry" xml:space="preserve">
    <value>Inquiry</value>
  </data>
  <data name="InquiryList" xml:space="preserve">
    <value>Inquiry list</value>
  </data>
  <data name="InquiryManagementPage" xml:space="preserve">
    <value>Inquiry management page</value>
  </data>
  <data name="InquiryName" xml:space="preserve">
    <value>Inquiry name</value>
  </data>
  <data name="ShowInInquiryTab" xml:space="preserve">
    <value>Show in inquiry tab</value>
  </data>
  <data name="MainExtraFeeType" xml:space="preserve">
    <value>Main Extra Fee Type</value>
  </data>
  <data name="RelatedMainFeeType" xml:space="preserve">
    <value>Related Main Extra Fee Type</value>
  </data>
  <data name="SubExtraFee" xml:space="preserve">
    <value>Sub Extra Fee</value>
  </data>
  <data name="MainFeeCategoryName" xml:space="preserve">
    <value>Main Fee Name</value>
  </data>
  <data name="AddMainExtraFeeCategory" xml:space="preserve">
    <value>Add Main Fee</value>
  </data>
  <data name="UpdateMainExtraFeeCategory" xml:space="preserve">
    <value>Update Main Fee</value>
  </data>
  <data name="MainExtraFeeCategoryList" xml:space="preserve">
    <value>Main Fee Types</value>
  </data>
  <data name="HasNoPermissionForOperation" xml:space="preserve">
    <value>You have no permission for this operation</value>
  </data>
  <data name="RelatedInsuranceApplicationCancellationType" xml:space="preserve">
    <value>There are exempt who have selected "Related Insurance" in the application. You select action!</value>
  </data>
  <data name="QuestionType" xml:space="preserve">
    <value>Question type</value>
  </data>
  <data name="UpdateInquiry" xml:space="preserve">
    <value>Update inquiry</value>
  </data>
  <data name="ColumnLength" xml:space="preserve">
    <value>Column Length</value>
  </data>
  <data name="ChoiceType" xml:space="preserve">
    <value>ChoiceType</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="AlreadyExistDataMessage" xml:space="preserve">
    <value>This question/answer has been used before, so it cannot be deleted</value>
  </data>
  <data name="ProvidedWithHasRelatedInsurance" xml:space="preserve">
    <value>Will insurance be provided for exempt individuals?</value>
  </data>
  <data name="CreateUpdate" xml:space="preserve">
    <value>Create/Update</value>
  </data>
  <data name="AddNewQuestion" xml:space="preserve">
    <value>Add New Question</value>
  </data>
  <data name="AddNewQuestionChoice" xml:space="preserve">
    <value>Add New Question Choice</value>
  </data>
  <data name="AddNewSelection" xml:space="preserve">
    <value>Add New Selection</value>
  </data>
  <data name="AddNewAnswer" xml:space="preserve">
    <value>Add New Answer</value>
  </data>
  <data name="ComboboxChoices" xml:space="preserve">
    <value>Combobox Choices</value>
  </data>
  <data name="QuestionAnswerSelections" xml:space="preserve">
    <value>Question Answer Selections</value>
  </data>
  <data name="UpdateInquiryDefinition" xml:space="preserve">
    <value>Update Inquiry Definition</value>
  </data>
  <data name="ShowInPmsPage" xml:space="preserve">
    <value>Show in PMS page</value>
  </data>
  <data name="RelatedService" xml:space="preserve">
    <value>Related Service</value>
  </data>
  <data name="NonApplicationRelatedInsurance" xml:space="preserve">
    <value>Non-Application Related Insurance</value>
  </data>
  <data name="Exception_EndOfShiftDate" xml:space="preserve">
    <value>Shift end time cannot be selected earlier than other hours</value>
  </data>
  <data name="Exception_EndOfLunchDate" xml:space="preserve">
    <value>Lunch start and end dates must match</value>
  </data>
  <data name="DataGroup" xml:space="preserve">
    <value>Data Group</value>
  </data>
  <data name="DataType" xml:space="preserve">
    <value>Data Type</value>
  </data>
  <data name="CancellationBy" xml:space="preserve">
    <value>Cancellation By</value>
  </data>
  <data name="ShowScoreCard" xml:space="preserve">
    <value>Show Score Card</value>
  </data>
  <data name="PMS" xml:space="preserve">
    <value>Performance Management System</value>
  </data>
  <data name="Exception_CreateSlot" xml:space="preserve">
    <value>
Slot could not be created</value>
  </data>
  <data name="ScoreCardReport" xml:space="preserve">
    <value>Score Card Report</value>
  </data>
  <data name="AddVideo" xml:space="preserve">
    <value>Add Video</value>
  </data>
  <data name="VideoScreenTimes" xml:space="preserve">
    <value>Video Screen Times</value>
  </data>
  <data name="AnnouncementScreeningTimes" xml:space="preserve">
    <value>Announcement Screen Times</value>
  </data>
  <data name="VideoName" xml:space="preserve">
    <value>Video Name</value>
  </data>
  <data name="EnterText" xml:space="preserve">
    <value>Enter text</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="UpdateVmsVideoTime" xml:space="preserve">
    <value>Update Video Screen Times</value>
  </data>
  <data name="UpdateVmsAnnouncementTime" xml:space="preserve">
    <value>Update Announcement Screen Times</value>
  </data>
  <data name="AddUpdateVmsAnnouncement" xml:space="preserve">
    <value>Add/Update Announcement</value>
  </data>
  <data name="LdapInvalidCredentials" xml:space="preserve">
    <value>Invalid username / password</value>
  </data>
  <data name="SlotReport" xml:space="preserve">
    <value>Slot Report</value>
  </data>
  <data name="SlotReportAgentColumn" xml:space="preserve">
    <value>Agent(VIP)</value>
  </data>
  <data name="SlotReportCallCenterColumn" xml:space="preserve">
    <value>Call Center(B2C)</value>
  </data>
  <data name="RemainingAppointment" xml:space="preserve">
    <value>Remaining Appointment</value>
  </data>
  <data name="SlotReportWalkinAppointment" xml:space="preserve">
    <value>Walkin Appointment</value>
  </data>
  <data name="ScanCycleStatus" xml:space="preserve">
    <value>Scan Cycle Status</value>
  </data>
  <data name="IhbInformation" xml:space="preserve">
    <value>IHB Information</value>
  </data>
  <data name="LdapAccountExpired" xml:space="preserve">
    <value>Your password has expired. Please contact your system administrator</value>
  </data>
  <data name="AddRelatedIndividual" xml:space="preserve">
    <value>Add Related Individual</value>
  </data>
  <data name="ReleatedInvidualInsurance" xml:space="preserve">
    <value>Will insurance be provided for exempt individuals?</value>
  </data>
  <data name="HasRelatedInsuranceWarning" xml:space="preserve">
    <value> Insurance policies will be issued for exempt indivuals, please make a selection from the Related Insurance item!</value>
  </data>
  <data name="HasRelatedInsuranceWarningCount" xml:space="preserve">
    <value>You must make insurance selections based on the number of Related Insurance entries provided!</value>
  </data>
  <data name="SentLinkInviduals" xml:space="preserve">
    <value>Send link for approved policy of excempt indivuals</value>
  </data>
  <data name="SendLink" xml:space="preserve">
    <value>Send Link</value>
  </data>
  <data name="ReleatedInsuranceSmsContentFirst" xml:space="preserve">
    <value>You can click link on below:</value>
  </data>
  <data name="InsuranceRefundCheckMessageScanSycle" xml:space="preserve">
    <value>Visa fee refunds will not be made for applications after [DATE], for [TYPE]</value>
  </data>
  <data name="CreatePrinterInsuranceFirstWarning" xml:space="preserve">
    <value>Please create insurance for the application for which you have received an ICR.</value>
  </data>
  <data name="PassportValidityPeriodForVisaTypeWarning" xml:space="preserve">
    <value>The passport validity period must be at least 2 years for the selected visa type. Please enter valid passport information.</value>
  </data>
  <data name="PrintIndividualICR" xml:space="preserve">
    <value>Print Individual ICR</value>
  </data>
  <data name="ADAccountName" xml:space="preserve">
    <value>AD Account Name</value>
  </data>
  <data name="AreYouSureToProcessScanOperationWithDiffrentPassport" xml:space="preserve">
    <value>The passport number entered during the appointment is different from the scanned passport number. If you proceed with the scan, it will be updated.</value>
  </data>
  <data name="Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="HasRelatedInsuranceIsRequired" xml:space="preserve">
    <value>
You must enter insurance information for exempt individuals!</value>
  </data>
  <data name="IsBiometricsDesktopUser" xml:space="preserve">
    <value>Biometrics Desktop User</value>
  </data>
  <data name="ScanCycleRefundInformation" xml:space="preserve">
    <value>Refund Information</value>
  </data>
  <data name="ScanCycleRefundInformationContent" xml:space="preserve">
    <value>Refund cannot be processed due to the visa type</value>
  </data>
  <data name="RejectionRefundStatusWarning" xml:space="preserve">
    <value>, Due to the visa category, it cannot be changed to the 'Refund is Done' status</value>
  </data>
  <data name="IsBlockedRefundIsDoneAllPolicies" xml:space="preserve">
    <value>No visa refund on all insurance</value>
  </data>
  <data name="PolicyDetail" xml:space="preserve">
    <value>Policy detail</value>
  </data>
  <data name="UpdateInsuranceRefundSetting" xml:space="preserve">
    <value>Update insurance visa rejection refund setting</value>
  </data>
  <data name="BranchInsuranceRefundSetting" xml:space="preserve">
    <value>Branch Insurance Refund Setting</value>
  </data>
  <data name="PleaseEnterDateValue" xml:space="preserve">
    <value>Please enter a date value</value>
  </data>
  <data name="NewMultipleVisaFee" xml:space="preserve">
    <value>New Multiple Visa Fee</value>
  </data>
  <data name="Successful" xml:space="preserve">
    <value>Successful</value>
  </data>
  <data name="PassportNumberChanged" xml:space="preserve">
    <value>The passport number has been changed</value>
  </data>
  <data name="QmsScreenTitle" xml:space="preserve">
    <value>Qms Screen Title</value>
  </data>
  <data name="AddNote" xml:space="preserve">
    <value>Add note</value>
  </data>
  <data name="ClickToSeeNote" xml:space="preserve">
    <value>Click to see note</value>
  </data>
  <data name="NoteCreatedBy" xml:space="preserve">
    <value>Note Created By</value>
  </data>
  <data name="NoteUpdatedBy" xml:space="preserve">
    <value>Note Updated By</value>
  </data>
  <data name="BlackListNotes" xml:space="preserve">
    <value>Black List Notes</value>
  </data>
  <data name="NumberOfEntryFee" xml:space="preserve">
    <value>Number of Entry Fee</value>
  </data>
  <data name="WarrantyPeriodExpired" xml:space="preserve">
    <value>Warranty Period Expired</value>
  </data>
  <data name="SlotStartDate" xml:space="preserve">
    <value>Slot Start Date</value>
  </data>
  <data name="SlotEndDate" xml:space="preserve">
    <value>Slot End Date</value>
  </data>
  <data name="PolicyPeriod" xml:space="preserve">
    <value>Policy period</value>
  </data>
  <data name="UploadArea" xml:space="preserve">
    <value>Upload area</value>
  </data>
  <data name="UploadEsim" xml:space="preserve">
    <value>Upload Esim</value>
  </data>
  <data name="Exception_TypeNotFound" xml:space="preserve">
    <value>Type not found</value>
  </data>
  <data name="Exception_FeeNotFoundFromFlag" xml:space="preserve">
    <value>Selected fee not found</value>
  </data>
  <data name="Exception_FeeNotFound" xml:space="preserve">
    <value>Fee not found</value>
  </data>
  <data name="Exception_FileNotFoundInCollection" xml:space="preserve">
    <value>File not found</value>
  </data>
  <data name="Exception_AlreadyExist" xml:space="preserve">
    <value>The record has been previously recorded</value>
  </data>
  <data name="Exception_UploadError" xml:space="preserve">
    <value>Error occured on upload</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Forgot Password/Update</value>
  </data>
  <data name="PasswordUpdateGuide" xml:space="preserve">
    <value>Password Update Guide</value>
  </data>
  <data name="ShowBranchInMobile" xml:space="preserve">
    <value>Show Branch in Mobile</value>
  </data>
  <data name="SMSSendingDate" xml:space="preserve">
    <value>SMS Sending Date</value>
  </data>
  <data name="EmailSendingDate" xml:space="preserve">
    <value>Email Sending Date</value>
  </data>
  <data name="NonApplicationRelatedInsuranceNotAllowed" xml:space="preserve">
    <value>You cannot create an application with the Non-Application Related Insurance type.</value>
  </data>
  <data name="Price2" xml:space="preserve">
    <value>Fee  2</value>
  </data>
  <data name="MainApplicantPhoneNumber" xml:space="preserve">
    <value>Main Applicant Phone Number</value>
  </data>
  <data name="RelatedInsurancePhoneNumber" xml:space="preserve">
    <value>Related Insurance Phone Number</value>
  </data>
  <data name="ImportApplicationHistoryData" xml:space="preserve">
    <value>Import to Data</value>
  </data>
  <data name="CannotEditWithIhbFile" xml:space="preserve">
    <value>This line cannot be edited because it contains an IHB document.</value>
  </data>
  <data name="IHBStatusNotFound" xml:space="preserve">
    <value>IHB status information not found</value>
  </data>
  <data name="IHBDocumentNumberNotFound" xml:space="preserve">
    <value>IHB document number information not found</value>
  </data>
  <data name="IHBNumberNotFound" xml:space="preserve">
    <value>IHB number information not found</value>
  </data>
  <data name="AlreadyHasIhbOrder" xml:space="preserve">
    <value>This application already has a request for the creation of an ongoing IHB</value>
  </data>
  <data name="ImportApplicationDataSuccessfullMessage" xml:space="preserve">
    <value>Data has been imported successfully</value>
  </data>
  <data name="TimePickerAmPmFormatView" xml:space="preserve">
    <value>h:mm tt</value>
  </data>
  <data name="NeurotecLicenseNumber" xml:space="preserve">
    <value>Neurotec license number</value>
  </data>
  <data name="LicenseNumber" xml:space="preserve">
    <value>License number</value>
  </data>
  <data name="LicenseType" xml:space="preserve">
    <value>License type</value>
  </data>
  <data name="NeurotecLicenses" xml:space="preserve">
    <value>Neurotec Licenses</value>
  </data>
  <data name="UpdateNeurotecLicense" xml:space="preserve">
    <value>Update Neurotec License</value>
  </data>
  <data name="NeurotecLicenseDetails" xml:space="preserve">
    <value>Neurotec License Details</value>
  </data>
  <data name="CompanyExtraFeePriceCurrency" xml:space="preserve">
    <value>Company Fee Currency</value>
  </data>
  <data name="Price2Currency" xml:space="preserve">
    <value>Fee 2 Currency</value>
  </data>
  <data name="ShowExtraFeeOnNewApplicationByRejectionStatus" xml:space="preserve">
    <value>Show in the re-application by rejection</value>
  </data>
  <data name="FreeCreateThreeMonthInsurance" xml:space="preserve">
    <value>Free Create 3 Month Insurance</value>
  </data>
  <data name="ThreeMonthCancelledInsuranceMessage" xml:space="preserve">
    <value> Since there is a 3-month insurance and rejection status (NLL and RI) in the last application, it is possible to continue with a free 3-month policy.</value>
  </data>
  <data name="RejectedFeesMadeVisible" xml:space="preserve">
    <value>Re-application Fees in Case of Rejection are now viewable again</value>
  </data>
  <data name="SixMonthCancelledInsuranceMessage" xml:space="preserve">
    <value>Since there is a 6-month insurance and rejection status (NLL and RI) in the last application, it is possible to continue with a free 6-month policy.</value>
  </data>
  <data name="OneYearCancelledInsuranceMessage" xml:space="preserve">
    <value>Since there is a 1-year insurance and rejection status (NLL and RI) in the last application, it is possible to continue with a free 1-year policy.</value>
  </data>
  <data name="FreeCreateThreeMonthInsuranceButtonLabel" xml:space="preserve">
    <value>Free create 3 month insurance</value>
  </data>
  <data name="FreeCreateSixMonthInsuranceButtonLabel" xml:space="preserve">
    <value>Free create 6 month insurance</value>
  </data>
  <data name="FreeCreateOneYearInsuranceButtonLabel" xml:space="preserve">
    <value>Free create 1 year insurance</value>
  </data>
  <data name="NoEmailLabelName" xml:space="preserve">
    <value>No Email</value>
  </data>
  <data name="CancellationOfRejected" xml:space="preserve">
    <value>Cancellation Of Rejected</value>
  </data>
  <data name="FirstInsuranceStartDate" xml:space="preserve">
    <value>First Insurance Start Date</value>
  </data>
  <data name="FirstInsuranceEndDate" xml:space="preserve">
    <value>First Insurance End Date</value>
  </data>
  <data name="FirstInsurancePolicyStatus" xml:space="preserve">
    <value>First Insurance Policy Status</value>
  </data>
  <data name="FirstReferenceNumber" xml:space="preserve">
    <value>First Reference Number</value>
  </data>
  <data name="FirstApplicationDate" xml:space="preserve">
    <value>First Application Date</value>
  </data>
  <data name="RelatedInsuranceSmsContentFooter" xml:space="preserve">
    <value>Support Line: +90 (850) 255 36 22</value>
  </data>
  <data name="InvalidPlateFormat" xml:space="preserve">
    <value>Invalid Plate Format! Please enter a valid license plate that starts with ‘999’ followed by a space. (Example: 999 AAA123)</value>
  </data>
  <data name="InvalidChassisNumber" xml:space="preserve">
    <value>Invalid Chassis Number! Please enter a valid 17-character chassis number using only letters and numbers(Example: VF1AB1CDEF1234567)</value>
  </data>
  <data name="BranchGeneralSituation" xml:space="preserve">
    <value>Branch General Situation</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="NotificationManagement" xml:space="preserve">
    <value>Notification Management</value>
  </data>
  <data name="SendTime" xml:space="preserve">
    <value>Send Time</value>
  </data>
  <data name="NotificationDetail" xml:space="preserve">
    <value>Notification Detail</value>
  </data>
  <data name="NotificationUpdate" xml:space="preserve">
    <value>Notification Update</value>
  </data>
  <data name="NotificationAdd" xml:space="preserve">
    <value>Notification Add</value>
  </data>
  <data name="NotificationSend" xml:space="preserve">
    <value>Notification Send</value>
  </data>
  <data name="UpdateSend" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="NotificationNumber" xml:space="preserve">
    <value>Notification Number</value>
  </data>
  <data name="NotificationTitle" xml:space="preserve">
    <value>Notification Title</value>
  </data>
  <data name="Notification" xml:space="preserve">
    <value>Notification</value>
  </data>
  <data name="Content" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="ScheduledTime" xml:space="preserve">
    <value>Scheduled Time</value>
  </data>
  <data name="AddNewNotification" xml:space="preserve">
    <value>Add new notifications</value>
  </data>
  <data name="SendNotification" xml:space="preserve">
    <value>Send notifications</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Send now</value>
  </data>
  <data name="UpdateMontagePolicy" xml:space="preserve">
    <value>Update Montage Policy</value>
  </data>
  <data name="ShowMontageVisaType" xml:space="preserve">
    <value>Show Montage Visa Type</value>
  </data>
  <data name="UpdateInsuranceStartDate" xml:space="preserve">
    <value>Update Insurance Start Date</value>
  </data>
  <data name="UpdateInsuranceEndDate" xml:space="preserve">
    <value>Update Insurance End Date</value>
  </data>
  <data name="ReferencePersonOrInstitution" xml:space="preserve">
    <value>Reference/Sponsor Person or Institution</value>
  </data>
  <data name="MainApplicantPassportNumber" xml:space="preserve">
    <value>Main Application Passport Number</value>
  </data>
  <data name="IsB2CPaymentActive" xml:space="preserve">
    <value>Is B2C payment active ?</value>
  </data>
  <data name="PaymentChannelType" xml:space="preserve">
    <value>Payment channel type</value>
  </data>
  <data name="PaymentProviders" xml:space="preserve">
    <value>Payment providers</value>
  </data>
  <data name="UpdateStudentPolicy" xml:space="preserve">
    <value>Update Student Policy</value>
  </data>
  <data name="IsShowInB2C" xml:space="preserve">
    <value>Show in B2C</value>
  </data>
  <data name="CustomerType" xml:space="preserve">
    <value>Customer Type</value>
  </data>
  <data name="InTime" xml:space="preserve">
    <value>In Time</value>
  </data>
  <data name="OutTime" xml:space="preserve">
    <value>Out Time</value>
  </data>
  <data name="WaitTime" xml:space="preserve">
    <value>Wait Time</value>
  </data>
  <data name="ServiceTime" xml:space="preserve">
    <value>Service Time</value>
  </data>
  <data name="TAT" xml:space="preserve">
    <value>TAT</value>
  </data>
  <data name="TimeDivision" xml:space="preserve">
    <value>Time Division</value>
  </data>
  <data name="SlaTime" xml:space="preserve">
    <value>SLA Time</value>
  </data>
  <data name="ExportToPdf" xml:space="preserve">
    <value>Export to PDF</value>
  </data>
  <data name="Duplicate" xml:space="preserve">
    <value>Duplicate</value>
  </data>
  <data name="NotificationDuplicate" xml:space="preserve">
    <value>Notification Duplicate</value>
  </data>
  <data name="PaymentChannel" xml:space="preserve">
    <value>Payment Channel</value>
  </data>
  <data name="PaymentStatus" xml:space="preserve">
    <value>Payment Status</value>
  </data>
  <data name="PaidAmount" xml:space="preserve">
    <value>Paid Amount</value>
  </data>
  <data name="NotificationSubject" xml:space="preserve">
    <value>Notification Subject</value>
  </data>
  <data name="MultipleApplicationPage" xml:space="preserve">
    <value>Multiple Application Page</value>
  </data>
  <data name="PrinterIcrLanguage" xml:space="preserve">
    <value>Printer Icr Language</value>
  </data>

  <data name="OnlineSuffix" xml:space="preserve">
    <value> (Online)</value>
  </data>
  <data name="CannotChangeFeeTakenFromOnline" xml:space="preserve">
    <value>A fee previously charged through online channels cannot be removed.</value>
  </data>
  <data name="QmsAzerbaijanTatReport" xml:space="preserve">
    <value>Azerbaijan TAT Report</value>
  </data>
  <data name="TicketNo" xml:space="preserve">
    <value>Ticket No</value>
  </data>
  <data name="VAC" xml:space="preserve">
    <value>VAC</value>
  </data>
  <data name="MissionName" xml:space="preserve">
    <value>Mission</value>
  </data>
  <data name="CreatedByPerson" xml:space="preserve">
    <value>Created By</value>
  </data>
  <data name="ApplicationLine" xml:space="preserve">
    <value>Application Line</value>
  </data>
  <data name="Service1InTime" xml:space="preserve">
    <value>Service 1 In Time</value>
  </data>
  <data name="Service1FirstCallTime" xml:space="preserve">
    <value>Service 1 First Call Time</value>
  </data>
  <data name="Service1OutTime" xml:space="preserve">
    <value>Service 1 Out Time</value>
  </data>
  <data name="Service1WaitTime" xml:space="preserve">
    <value>Service 1 Wait Time</value>
  </data>
  <data name="Service1ServiceTime" xml:space="preserve">
    <value>Service 1 Service Time</value>
  </data>
  <data name="TotalTat" xml:space="preserve">
    <value>Total TAT</value>
  </data>
  <data name="TokenStatus" xml:space="preserve">
    <value>Token Status</value>
  </data>

  <data name="GenericOptionList" xml:space="preserve">
    <value>Generic option list</value>
  </data>
  <data name="Scope" xml:space="preserve">
    <value>Scope</value>
  </data>
  <data name="AddGenericOption" xml:space="preserve">
    <value>Add generic option</value>
  </data>
  <data name="GenericOption" xml:space="preserve">
    <value>Generic option</value>
  </data>
  <data name="UpdateGenericOption" xml:space="preserve">
    <value>Update generic option</value>
  </data>
  <data name="GenericOptionDetails" xml:space="preserve">
    <value>Generic option details</value>
  </data>
  <data name="PleaseEnterIntegerValue" xml:space="preserve">
    <value>Please enter integer value</value>
  </data>
  <data name="RelevantRecord" xml:space="preserve">
    <value>Relevant record</value>
  </data>
  <data name="ClientConfiguration" xml:space="preserve">
    <value>Client configuration</value>
  </data>

</root>