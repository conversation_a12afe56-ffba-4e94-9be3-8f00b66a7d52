@model List<dynamic>

<div class="card card-custom card-stretch">
    <div class="card-header">
        <div class="card-title">
            <h3 class="card-label">
                @SiteResources.RejectedApplicationsByPhone.ToTitleCase()
            </h3>
        </div>
    </div>
    <div class="card-body">
        @if (Model != null && Model.Any())
        {
            <div class="alert alert-warning" role="alert">
                <strong>@SiteResources.Warning.ToTitleCase():</strong> @SiteResources.PreviousRejectedApplicationsFoundByPhone
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>@SiteResources.ApplicationTime.ToTitleCase()</th>
                            <th>@SiteResources.Name.ToTitleCase()</th>
                            <th>@SiteResources.Surname.ToTitleCase()</th>
                            <th>@SiteResources.PassportNumber.ToTitleCase()</th>
                            <th>@SiteResources.PhoneNumber.ToTitleCase()</th>
                            <th>@SiteResources.RejectionDate.ToTitleCase()</th>
                            <th>@SiteResources.Actions.ToTitleCase()</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var application in Model)
                        {
                            var rejectionHistory = application.StatusHistories?.FirstOrDefault();
                            <tr>
                                <td>@application.ApplicationTime.ToString("dd/MM/yyyy")</td>
                                <td>@application.Name</td>
                                <td>@application.Surname</td>
                                <td>@application.PassportNumber</td>
                                <td>@application.PhoneNumber1</td>
                                <td>
                                    @if (rejectionHistory != null)
                                    {
                                        @rejectionHistory.StatusDate.ToString("dd/MM/yyyy")
                                    }
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" onclick="openApplicationDetail('@application.EncryptedId')">
                                        @SiteResources.ViewDetails.ToTitleCase()
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
            
            <div class="alert alert-info mt-3" role="alert">
                <strong>@SiteResources.Note.ToTitleCase():</strong> @SiteResources.PhoneRejectionCheckNote
            </div>
        }
        else
        {
            <div class="alert alert-info" role="alert">
                @SiteResources.NoRejectedApplicationsFound
            </div>
        }
    </div>
</div>

<script>
    function openApplicationDetail(encryptedId) {
        // Open application detail in new tab/window
        var url = '@Url.Action("Detail", "Application", new { Area = "Appointment" })' + '?encryptedId=' + encryptedId;
        window.open(url, '_blank');
    }
</script>
