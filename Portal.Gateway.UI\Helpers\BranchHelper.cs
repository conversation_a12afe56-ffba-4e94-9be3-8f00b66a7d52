using System.Collections.Generic;
using System.Linq;

namespace Portal.Gateway.UI.Helpers
{
    public static class BranchHelper
    {
        /// <summary>
        /// Iraq branch IDs based on the existing codebase patterns
        /// </summary>
        private static readonly HashSet<int> IraqBranchIds = new HashSet<int>
        {
            3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 33, 40, 41, 42
        };

        /// <summary>
        /// Determines if the given branch ID is an Iraq branch
        /// </summary>
        /// <param name="branchId">The branch ID to check</param>
        /// <returns>True if the branch is an Iraq branch, false otherwise</returns>
        public static bool IsIraqBranch(int? branchId)
        {
            return branchId.HasValue && IraqBranchIds.Contains(branchId.Value);
        }

        /// <summary>
        /// Determines if the given branch ID is an Iraq branch
        /// </summary>
        /// <param name="branchId">The branch ID to check</param>
        /// <returns>True if the branch is an Iraq branch, false otherwise</returns>
        public static bool IsIraqBranch(int branchId)
        {
            return IraqBranchIds.Contains(branchId);
        }

        /// <summary>
        /// Gets all Iraq branch IDs
        /// </summary>
        /// <returns>Collection of Iraq branch IDs</returns>
        public static IEnumerable<int> GetIraqBranchIds()
        {
            return IraqBranchIds.AsEnumerable();
        }
    }
}
