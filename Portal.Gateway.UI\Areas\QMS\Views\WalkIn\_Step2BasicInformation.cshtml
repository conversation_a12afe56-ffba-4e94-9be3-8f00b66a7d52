﻿@model QmsWalkInViewModel

<div id="divStep2BasicInformation" class="pb-5" data-wizard-type="step-content">
    <h4 class="mb-10 font-weight-bold text-dark">@SiteResources.PreApplicationStep3BasicInformation.ToTitleCase()</h4>
    <div class="row">
        @{
            for (var i = 0; i < Model.Applicants.Count; i++)
            {
                <div class="col-xl-12" id="ApplicantForm-@(i)" style="display: none;">
                    <input type="hidden" data-order="@i" asp-for="Applicants[i].ApplicationEnabled" />
                    <div class="card card-custom gutter-b applicant-form">
                        <div class="card-header">
                            <div class="card-title">
                                <h3 class="card-label">@SiteResources.Applicant.ToTitleCase() @(i+1)</h3>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="example">
                                <div class="example-preview">
                                    <div id="ApplicantForm-Validation-@(i)">
                                        <div class="form-group row">
                                            <div class="col-lg-6">
                                                <label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
                                                <input type="text" asp-for="Applicants[i].Name" class="form-control" autocomplete="off" />
                                                <span asp-validation-for="Applicants[i].Name"></span>
                                            </div>
                                            <div class="col-lg-6">
                                                <label class="font-weight-bold">@SiteResources.Surname.ToTitleCase()</label>
                                                <input type="text" asp-for="Applicants[i].Surname" class="form-control" autocomplete="off" />
                                                <span asp-validation-for="Applicants[i].Surname"></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-lg-6">
                                                <label class="font-weight-bold">@SiteResources.BirthDate.ToTitleCase()</label>
                                                @(Html.Kendo().DatePickerFor(m => m.Applicants[i].BirthDate).Format(SiteResources.DatePickerFormatView).Max(DateTime.Now).HtmlAttributes(new { @class = "form-control" }))
                                                <span asp-validation-for="Applicants[i].BirthDate"></span>
                                            </div>
                                            <div class="col-lg-6">
                                                <label class="font-weight-bold">@SiteResources.Gender.ToTitleCase()</label>
                                                @(Html.Kendo().DropDownListFor(m=> m.Applicants[i].GenderId)
                                            .HtmlAttributes(new { @class = "form-control" })
                                            .Filter(FilterType.Contains)
                                            .OptionLabel(SiteResources.Select)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .DataSource(source => {
                                                source.Read(read =>
                                                {
                                                    read.Action("GetGenderSelectList", "Parameter", new { Area = "" });
                                                });
                                            }))
                                                <span asp-validation-for="Applicants[i].GenderId"></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-lg-4">
                                                <label class="font-weight-bold">@SiteResources.Nationality.ToTitleCase()</label>
                                                @(Html.Kendo().DropDownListFor(m=> m.Applicants[i].NationalityId)
                                            .HtmlAttributes(new { @class = "form-control" })
                                            .Filter(FilterType.Contains)
                                            .OptionLabel(SiteResources.Select)
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .DataSource(source => {
                                                source.Read(read =>
                                                {
                                                    read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
                                                });
                                            }))
                                                <span asp-validation-for="Applicants[i].NationalityId"></span>
                                            </div>
                                            <div class="col-lg-4">
                                                <label class="font-weight-bold">@SiteResources.PassportNumber.ToTitleCase()</label>
                                                <input type="text" asp-for="Applicants[i].PassportNumber" class="form-control" autocomplete="off" />
                                                <span asp-validation-for="Applicants[i].PassportNumber"></span>
                                            </div>
                                            <div class="col-lg-4">
                                                <label class="font-weight-bold">@SiteResources.PassportExpireDate.ToTitleCase()</label>
                                                @(Html.Kendo().DatePickerFor(m => m.Applicants[i].PassportExpireDate).Format(SiteResources.DatePickerFormatView).Min(DateTime.Now))
                                                <span asp-validation-for="Applicants[i].PassportExpireDate"></span>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <div class="col-lg-6">
                                                <label class="font-weight-bold">@SiteResources.Email.ToTitleCase()</label>
                                                <input type="email" asp-for="Applicants[i].Email" class="form-control pass-validation" autocomplete="off" />
                                            </div>
                                            <div class="col-lg-6">
                                                <label class="font-weight-bold">@SiteResources.PhoneNumber.ToTitleCase()</label>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <div class="input-group-text"> 
                                                            <span>@Model.CountryCallingCode</span>
                                                        </div>   
                                                    </div>
                                                    <input type="text" asp-for="Applicants[i].PhoneNumber" class="form-control pass-validation" nameValid="PhoneNumber" autocomplete="off" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group validated row">
                                            <div class="col-lg-12">
                                                <a href="javascript:void(0);" onclick="displayPassportInformation('@i');" class="btn btn-sm btn-outline-primary font-weight-bold">@SiteResources.DisplayPassportInformation.ToTitleCase() (@SiteResources.Applicant.ToTitleCase() @(i+1))</a>
                                                <a href="javascript:void(0);" onclick="scanPassportAndUpdateInformation('@i');" class="btn btn-sm btn-success font-weight-bold">@SiteResources.ScanPassportAndUpdateInformation.ToTitleCase() (@SiteResources.Applicant.ToTitleCase() @(i+1))</a>
                                                <code class="d-block mt-5" id="passportData-@i"></code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="example-code">
                                    <div class="example-highlight" style="text-align:right">
                                        <a href="javascript:void(0)" class="btn btn-danger font-weight-bold" onclick="DisableApplicantForm(@i)">@SiteResources.Remove.ToTitleCase()</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>
